# Caddyfile - Configuración optimizada para Media Server
# Versión mejorada con seguridad, rendimiento y mejores prácticas

# === CONFIGURACIÓN GLOBAL ===
{
	# Configuración global de seguridad y rendimiento
	servers {
		timeouts {
			read_body 30s
			read_header 10s
			write 30s
			idle 120s
		}
	}
}

# === JELLYFIN MEDIA SERVER ===
tankeflix.duckdns.org {
	# Compresión para mejor rendimiento
	encode {
		gzip 6
		zstd
	}

	# Configuración de seguridad y rendimiento

	# Proxy inverso hacia Jellyfin con configuración optimizada
	reverse_proxy localhost:8096 {
		# Headers esenciales para Jellyfin
		header_up Host {upstream_hostport}
		header_up X-Real-IP {remote_host}

		# Timeouts específicos para streaming
		transport http {
			read_timeout 60s
			write_timeout 60s
			dial_timeout 10s
		}
	}

	# Headers de seguridad optimizados para Jellyfin
	header {
		# Seguridad básica
		X-Content-Type-Options "nosniff"
		X-Frame-Options "SAMEORIGIN" # Cambiado de DENY para permitir embeds de Jellyfin
		Referrer-Policy "strict-origin-when-cross-origin"

		# HSTS con configuración robusta
		Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

		# CSP relajado para Jellyfin (necesita inline scripts y estilos)
		Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: *; media-src 'self' blob: data: *"

		# Headers adicionales de seguridad
		Permissions-Policy "geolocation=(), microphone=(), camera=()"
		X-Permitted-Cross-Domain-Policies "none"

		# Ocultar información del servidor
		-Server
	}

	# Logging mejorado con filtros
	log {
		output file C:\Cosas\Caddy\Logs\Jellyfin\jellyfin.log {
			roll_size 50MiB
			roll_keep 10
			roll_keep_for 720h # 30 días
		}
		format json
		level INFO
	}

	# Manejo de errores personalizado
	handle_errors {
		@5xx expression {http.error.status_code} >= 500
		respond @5xx "Servicio temporalmente no disponible" 503

		@4xx expression {http.error.status_code} >= 400
		respond @4xx "Recurso no encontrado" 404
	}

	# TLS con configuración segura
	tls <EMAIL> {
		protocols tls1.2 tls1.3
	}
}

# === QBITTORRENT TORRENT CLIENT ===
tanketorrent.duckdns.org {
	# Compresión para la interfaz web
	encode {
		gzip 6
		zstd
	}

	# Configuración de seguridad para torrent client

	# Proxy inverso hacia qBittorrent con timeouts apropiados
	reverse_proxy localhost:8099 {
		header_up Host {upstream_hostport}
		header_up X-Real-IP {remote_host}

		# Timeouts para operaciones de torrent
		transport http {
			read_timeout 30s
			write_timeout 30s
			dial_timeout 5s
		}
	}

	# Headers de seguridad estrictos para herramienta administrativa
	header {
		X-Content-Type-Options "nosniff"
		X-Frame-Options "DENY" # Más estricto para admin tools
		Referrer-Policy "strict-origin-when-cross-origin"
		Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

		# CSP estricto para admin interface
		Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"

		# Headers adicionales
		Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=()"
		X-Permitted-Cross-Domain-Policies "none"
		-Server
	}

	# Logging optimizado
	log {
		output file C:\Cosas\Caddy\Logs\Qbittorrent\qbittorrent.log {
			roll_size 25MiB
			roll_keep 8
			roll_keep_for 480h # 20 días
		}
		format json
		level INFO
	}

	# Manejo de errores
	handle_errors {
		@5xx expression {http.error.status_code} >= 500
		respond @5xx "qBittorrent temporalmente no disponible" 503

		@4xx expression {http.error.status_code} >= 400
		respond @4xx "Acceso no autorizado" 401
	}

	# TLS seguro
	tls <EMAIL> {
		protocols tls1.2 tls1.3
	}
}

# === SNIPPET REUTILIZABLE PARA APLICACIONES *ARR ===
(arr_common) {
	# Compresión para interfaces web
	encode {
		gzip 6
		zstd
	}

	# Configuración de seguridad para aplicaciones *arr

	# Headers de seguridad para aplicaciones *arr
	header {
		X-Content-Type-Options "nosniff"
		X-Frame-Options "SAMEORIGIN" # Permite embeds para notificaciones
		Referrer-Policy "strict-origin-when-cross-origin"
		Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

		# CSP balanceado para funcionalidad completa
		Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: *"

		Permissions-Policy "geolocation=(), microphone=(), camera=()"
		X-Permitted-Cross-Domain-Policies "none"
		-Server
	}

	# Manejo de errores consistente
	handle_errors {
		@5xx expression {http.error.status_code} >= 500
		respond @5xx "{args[1]} temporalmente no disponible" 503

		@4xx expression {http.error.status_code} >= 400
		respond @4xx "Recurso no encontrado en {args[1]}" 404
	}

	# TLS seguro
	tls <EMAIL> {
		protocols tls1.2 tls1.3
	}
}

# === APLICACIONES *ARR CON DOMINIOS SEPARADOS ===

# Sonarr - Gestión de Series
tankesonarr.duckdns.org {
	# Importar configuración común
	import arr_common sonarr "Sonarr"

	# Proxy específico para Sonarr
	reverse_proxy localhost:8989 {
		header_up Host {upstream_hostport}
		header_up X-Real-IP {remote_host}

		# Timeouts optimizados para operaciones de series
		transport http {
			read_timeout 45s
			write_timeout 45s
			dial_timeout 5s
		}
	}

	# Logging específico para Sonarr
	log {
		output file C:\Cosas\Caddy\Logs\Sonarr\sonarr.log {
			roll_size 30MiB
			roll_keep 8
			roll_keep_for 480h # 20 días
		}
		format json
		level INFO
	}
}

# Jackett - Indexador de Torrents
tankejackett.duckdns.org {
	# Importar configuración común
	import arr_common jackett "Jackett"

	# Proxy específico para Jackett
	reverse_proxy localhost:9117 {
		header_up Host {upstream_hostport}
		header_up X-Real-IP {remote_host}

		# Timeouts para búsquedas de indexadores
		transport http {
			read_timeout 60s # Búsquedas pueden tomar tiempo
			write_timeout 30s
			dial_timeout 5s
		}
	}

	# Logging específico para Jackett
	log {
		output file C:\Cosas\Caddy\Logs\Jackett\jackett.log {
			roll_size 25MiB
			roll_keep 6
			roll_keep_for 360h # 15 días
		}
		format json
		level INFO
	}
}

# Prowlarr - Gestión de Indexadores
tankeprowlarr.duckdns.org {
	# Importar configuración común
	import arr_common prowlarr "Prowlarr"

	# Proxy específico para Prowlarr
	reverse_proxy localhost:9696 {
		header_up Host {upstream_hostport}
		header_up X-Real-IP {remote_host}

		# Timeouts para operaciones de indexadores
		transport http {
			read_timeout 45s
			write_timeout 30s
			dial_timeout 5s
		}
	}

	# Logging específico para Prowlarr
	log {
		output file C:\Cosas\Caddy\Logs\Prowlarr\prowlarr.log {
			roll_size 25MiB
			roll_keep 6
			roll_keep_for 360h # 15 días
		}
		format json
		level INFO
	}
}

# Radarr - Gestión de Películas
tankeradarr.duckdns.org {
	# Importar configuración común
	import arr_common radarr "Radarr"

	# Proxy específico para Radarr
	reverse_proxy localhost:7878 {
		header_up Host {upstream_hostport}
		header_up X-Real-IP {remote_host}

		# Timeouts optimizados para operaciones de películas
		transport http {
			read_timeout 45s
			write_timeout 45s
			dial_timeout 5s
		}
	}

	# Logging específico para Radarr
	log {
		output file C:\Cosas\Caddy\Logs\Radarr\radarr.log {
			roll_size 30MiB
			roll_keep 8
			roll_keep_for 480h # 20 días
		}
		format json
		level INFO
	}
}

# Jellyseerr - Gestión de Solicitudes para Jellyfin
tankejellyseerr.duckdns.org {
	# Importar configuración común para aplicaciones de gestión
	import arr_common jellyseerr "Jellyseerr"

	# Proxy específico para Jellyseerr
	reverse_proxy localhost:5055 {
		header_up Host {upstream_hostport}
		header_up X-Real-IP {remote_host}

		# Timeouts optimizados para solicitudes y notificaciones
		transport http {
			read_timeout 45s
			write_timeout 45s
			dial_timeout 5s
		}
	}

	# Logging específico para Jellyseerr
	log {
		output file C:\Cosas\Caddy\Logs\Jellyseerr\jellyseerr.log {
			roll_size 30MiB
			roll_keep 8
			roll_keep_for 480h # 20 días
		}
		format json
		level INFO
	}
}

# Ntfy - Sistema de Notificaciones Self-hosted
tankenotify.duckdns.org {
	# Compresión para la interfaz web
	encode {
		gzip 6
		zstd
	}

	# Proxy hacia ntfy con configuración optimizada
	reverse_proxy localhost:8080 {
		header_up Host {upstream_hostport}
		header_up X-Real-IP {remote_host}

		# Headers específicos para WebSockets (ntfy usa SSE)
		header_up Connection {>Connection}
		header_up Upgrade {>Upgrade}

		# Timeouts para conexiones persistentes
		transport http {
			read_timeout 300s  # 5 minutos para SSE
			write_timeout 60s
			dial_timeout 5s
		}
	}

	# Headers de seguridad para aplicación de notificaciones
	header {
		X-Content-Type-Options "nosniff"
		X-Frame-Options "SAMEORIGIN"  # Permite embeds para notificaciones
		Referrer-Policy "strict-origin-when-cross-origin"
		Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

		# CSP para ntfy (necesita WebSockets y API calls)
		Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; connect-src 'self' wss: ws:; img-src 'self' data:"

		Permissions-Policy "geolocation=(), microphone=(), camera=()"
		X-Permitted-Cross-Domain-Policies "none"
		-Server
	}

	# Logging específico para ntfy
	log {
		output file C:\Cosas\Caddy\Logs\Ntfy\ntfy.log {
			roll_size 25MiB
			roll_keep 6
			roll_keep_for 360h  # 15 días
		}
		format json
		level INFO
	}

	# Manejo de errores
	handle_errors {
		@5xx expression {http.error.status_code} >= 500
		respond @5xx "Sistema de notificaciones temporalmente no disponible" 503

		@4xx expression {http.error.status_code} >= 400
		respond @4xx "Acceso no autorizado al sistema de notificaciones" 401
	}

}


cardanza.duckdns.org {
    # Ruta donde Caddy buscará tus archivos web
    root * C:\Cosas\Pagina\index.html  # En Windows, usa la ruta completa con barras invertidas
    # root * /home/<USER>/mi_sitio_web_prueba # En Linux/macOS, usa la ruta completa

    # Sirve los archivos estáticos
    file_server

    # Habilita compresión Gzip para mejorar el rendimiento
    encode gzip zstd

    # Manejo de errores
    handle_errors {
        respond "{http.error.status_code} {http.error.status_text}"
    }

    # Opcional: Redirige HTTP a HTTPS automáticamente
    # Caddy hace esto por defecto con Let's Encrypt, pero es bueno saber que existe.
    # En la mayoría de los casos, si tienes la IP abierta en los puertos 80 y 443,
    # Caddy automáticamente gestionará los certificados SSL/TLS con Let's Encrypt.
		# TLS seguro
	tls <EMAIL> {
		protocols tls1.2 tls1.3
	}
}