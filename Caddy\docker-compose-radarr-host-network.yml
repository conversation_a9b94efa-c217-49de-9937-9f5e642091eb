# Docker Compose para Radarr con red host
# Solución para problemas de conectividad Docker→Host

version: '3.8'

services:
  radarr:
    image: lscr.io/linuxserver/radarr:latest
    container_name: radarr
    restart: unless-stopped
    
    # Usar red del host para acceso directo a qBittorrent
    network_mode: host
    
    # Variables de entorno
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - UMASK=002
    
    # Volúmenes (ajusta según tu configuración)
    volumes:
      - ./radarr/config:/config
      - ./downloads:/downloads
      - ./movies:/movies
    
    # Con network_mode: host, no necesitas ports
    # El contenedor usará directamente el puerto 7878 del host

# Nota: Con network_mode: host, Radarr podrá acceder a qBittorrent
# usando localhost:8099 directamente, como si fuera una aplicación nativa
