# Stack completo en Docker (OPCIONAL - tu configuración actual es mejor)
# Solo para referencia si quisieras experimentar

version: '3.8'

networks:
  media-network:
    driver: bridge

services:
  # Caddy como proxy reverso
  caddy:
    image: caddy:latest
    container_name: caddy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile
      - ./caddy-data:/data
      - ./caddy-config:/config
    networks:
      - media-network

  # Radarr
  radarr:
    image: lscr.io/linuxserver/radarr:latest
    container_name: radarr
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
    volumes:
      - ./radarr/config:/config
      - ./downloads:/downloads
      - ./movies:/movies
    networks:
      - media-network

  # qBittorrent (en Docker)
  qbittorrent:
    image: lscr.io/linuxserver/qbittorrent:latest
    container_name: qbittorrent
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - WEBUI_PORT=8080
    volumes:
      - ./qbittorrent/config:/config
      - ./downloads:/downloads
    networks:
      - media-network

# NOTA: Esta configuración requeriría:
# 1. Migrar qBittorrent a Docker
# 2. Reconfigurar todos los paths
# 3. Migrar la configuración de Caddy
# 
# Tu configuración actual (Caddy nativo + apps Docker) es MEJOR
