# Docker Compose para todo el stack de media
# Solución completa para comunicación entre contenedores

version: '3.8'

# Red personalizada para comunicación entre contenedores
networks:
  media-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

services:
  # Radarr - Gestión de películas
  radarr:
    image: lscr.io/linuxserver/radarr:latest
    container_name: radarr
    restart: unless-stopped
    
    ports:
      - "7878:7878"
    
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - UMASK=002
    
    volumes:
      - ./radarr/config:/config
      - ./downloads:/downloads
      - ./movies:/movies
    
    # Configuración para acceso al host (qBittorrent)
    extra_hosts:
      - "qbittorrent:host-gateway"
      - "host.docker.internal:host-gateway"
    
    networks:
      - media-network
    
    # Dependencias
    depends_on:
      - jackett

  # Jackett - Indexadores
  jackett:
    image: lscr.io/linuxserver/jackett:latest
    container_name: jackett
    restart: unless-stopped
    
    ports:
      - "9117:9117"
    
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - AUTO_UPDATE=true
    
    volumes:
      - ./jackett/config:/config
      - ./downloads:/downloads
    
    networks:
      - media-network

  # Sonarr - Gestión de series (si también está en Docker)
  sonarr:
    image: lscr.io/linuxserver/sonarr:latest
    container_name: sonarr
    restart: unless-stopped
    
    ports:
      - "8989:8989"
    
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - UMASK=002
    
    volumes:
      - ./sonarr/config:/config
      - ./downloads:/downloads
      - ./tv:/tv
    
    extra_hosts:
      - "qbittorrent:host-gateway"
      - "host.docker.internal:host-gateway"
    
    networks:
      - media-network
    
    depends_on:
      - jackett

  # Prowlarr - Gestión de indexadores (alternativa a Jackett)
  prowlarr:
    image: lscr.io/linuxserver/prowlarr:latest
    container_name: prowlarr
    restart: unless-stopped
    
    ports:
      - "9696:9696"
    
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
    
    volumes:
      - ./prowlarr/config:/config
    
    networks:
      - media-network

# Con esta configuración:
# - Radarr puede acceder a Jackett usando: http://jackett:9117
# - Radarr puede acceder a qBittorrent usando: http://host.docker.internal:8099
# - Sonarr puede acceder a Jackett usando: http://jackett:9117
# - Todos los contenedores están en la misma red y pueden comunicarse por nombre
