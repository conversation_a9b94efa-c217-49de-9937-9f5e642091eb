# Docker Compose para Ntfy - Sistema de notificaciones self-hosted
# Configuración optimizada para media server stack

version: '3.8'

services:
  ntfy:
    image: binwiederhier/ntfy:latest
    container_name: ntfy
    restart: unless-stopped
    
    # Configuración de puertos
    ports:
      - "8080:80"  # Puerto para la interfaz web y API
    
    # Volúmenes para persistencia
    volumes:
      - ./ntfy/cache:/var/cache/ntfy
      - ./ntfy/etc:/etc/ntfy
      - ./ntfy/data:/var/lib/ntfy
    
    # Variables de entorno
    environment:
      - NTFY_BASE_URL=https://tankenotify.duckdns.org
      - NTFY_CACHE_FILE=/var/cache/ntfy/cache.db
      - NTFY_AUTH_FILE=/var/lib/ntfy/user.db
      - NTFY_AUTH_DEFAULT_ACCESS=deny-all
      - NTFY_BEHIND_PROXY=true
      - NTFY_ENABLE_SIGNUP=false
      - NTFY_ENABLE_LOGIN=true
    
    # Comando con configuración personalizada
    command: 
      - serve
      - --cache-file=/var/cache/ntfy/cache.db
      - --auth-file=/var/lib/ntfy/user.db
      - --auth-default-access=deny-all
      - --behind-proxy
      - --base-url=https://tankenotify.duckdns.org
    
    # Configuración de salud
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Configuración de red
    networks:
      - ntfy-network

networks:
  ntfy-network:
    driver: bridge

# Volúmenes nombrados para mejor gestión
volumes:
  ntfy-cache:
  ntfy-etc:
  ntfy-data:
