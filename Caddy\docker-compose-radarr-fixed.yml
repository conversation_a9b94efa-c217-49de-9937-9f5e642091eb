# Docker Compose para Radarr con network host
# Solución definitiva para problemas de conectividad con qBittorrent

version: '3.8'

services:
  radarr:
    image: lscr.io/linuxserver/radarr:latest
    container_name: radarr-fixed
    restart: unless-stopped
    
    # Usar red del host para acceso directo (solución definitiva)
    network_mode: host
    
    # Variables de entorno
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - UMASK=002
    
    # Volúmenes (copia los mismos paths de tu configuración actual)
    volumes:
      - ./radarr/config:/config
      - ./downloads:/downloads
      - ./movies:/movies
    
    # Con network_mode: host:
    # - Radarr usará directamente la red del host
    # - Podrá acceder a qBittorrent usando localhost:8099
    # - No habrá problemas de conectividad Docker→Host
    # - Funcionará exactamente como una aplicación nativa

# Para migrar desde tu contenedor actual:
# 1. docker-compose down (para parar el contenedor actual)
# 2. docker-compose -f docker-compose-radarr-fixed.yml up -d
# 3. Configurar qBittorrent en Radarr con localhost:8099
