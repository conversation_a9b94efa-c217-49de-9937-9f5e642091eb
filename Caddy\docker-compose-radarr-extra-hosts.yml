# Docker Compose para Radarr con extra_hosts
# Alternativa para mejorar conectividad Docker→Host

version: '3.8'

services:
  radarr:
    image: lscr.io/linuxserver/radarr:latest
    container_name: radarr
    restart: unless-stopped
    
    # Puertos
    ports:
      - "7878:7878"
    
    # Variables de entorno
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - UMASK=002
    
    # Volúmenes (ajusta según tu configuración)
    volumes:
      - ./radarr/config:/config
      - ./downloads:/downloads
      - ./movies:/movies
    
    # Mapeo de hosts para mejor conectividad
    extra_hosts:
      - "qbittorrent:host-gateway"
      - "host.docker.internal:host-gateway"
    
    # Red personalizada
    networks:
      - media-network

networks:
  media-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Con esta configuración, Radarr puede usar:
# - qbittorrent:8099
# - host.docker.internal:8099
# - host-gateway:8099
