2025-05-25 21:35:04.5|Debug|ProcessProvider|Found 0 processes with the name: Radarr.Console
2025-05-25 21:35:04.5|Debug|ProcessProvider|Found 1 processes with the name: Radarr
2025-05-25 21:35:04.5|Debug|ProcessProvider| - [177] Radarr
2025-05-25 21:35:04.5|Info|MigrationController|*** Migrating data source=/config/logs.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-05-25 21:35:04.5|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-05-25 21:35:04.5|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-05-25 21:35:04.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-05-25 21:35:04.6|Info|DatabaseEngineVersionCheck|SQLite 3.48.0
2025-05-25 21:35:04.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0287478s
2025-05-25 21:35:04.6|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-05-25 21:35:04.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.037361s
2025-05-25 21:35:04.6|Info|FluentMigrator.Runner.MigrationRunner|VersionMigration migrating
2025-05-25 21:35:04.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:04.6|Info|FluentMigrator.Runner.MigrationRunner|CreateTable VersionInfo
2025-05-25 21:35:04.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|CREATE TABLE "VersionInfo" ("Version" INTEGER NOT NULL)
2025-05-25 21:35:04.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0098118s
2025-05-25 21:35:04.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:04.7|Info|FluentMigrator.Runner.MigrationRunner|VersionMigration migrated
2025-05-25 21:35:04.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0335314s
2025-05-25 21:35:04.7|Info|FluentMigrator.Runner.MigrationRunner|VersionUniqueMigration migrating
2025-05-25 21:35:04.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:04.7|Info|FluentMigrator.Runner.MigrationRunner|CreateIndex VersionInfo (Version)
2025-05-25 21:35:04.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|CREATE UNIQUE INDEX "UC_Version" ON "VersionInfo" ("Version" ASC)
2025-05-25 21:35:04.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0095368s
2025-05-25 21:35:04.7|Info|FluentMigrator.Runner.MigrationRunner|AlterTable VersionInfo
2025-05-25 21:35:04.8|Info|FluentMigrator.Runner.MigrationRunner|=> 3.5E-06s
2025-05-25 21:35:04.8|Info|FluentMigrator.Runner.MigrationRunner|CreateColumn VersionInfo AppliedOn DateTime
2025-05-25 21:35:04.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|ALTER TABLE "VersionInfo" ADD COLUMN "AppliedOn" DATETIME
2025-05-25 21:35:04.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0099109s
2025-05-25 21:35:04.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:04.8|Info|FluentMigrator.Runner.MigrationRunner|VersionUniqueMigration migrated
2025-05-25 21:35:04.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0296506s
2025-05-25 21:35:04.8|Info|FluentMigrator.Runner.MigrationRunner|VersionDescriptionMigration migrating
2025-05-25 21:35:04.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:04.9|Info|FluentMigrator.Runner.MigrationRunner|AlterTable VersionInfo
2025-05-25 21:35:04.9|Info|FluentMigrator.Runner.MigrationRunner|=> 3E-06s
2025-05-25 21:35:04.9|Info|FluentMigrator.Runner.MigrationRunner|CreateColumn VersionInfo Description String
2025-05-25 21:35:04.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|ALTER TABLE "VersionInfo" ADD COLUMN "Description" TEXT
2025-05-25 21:35:04.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0091462s
2025-05-25 21:35:04.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:04.9|Info|FluentMigrator.Runner.MigrationRunner|VersionDescriptionMigration migrated
2025-05-25 21:35:04.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0273726s
2025-05-25 21:35:04.9|Info|FluentMigrator.Runner.MigrationRunner|1: InitialSetup migrating
2025-05-25 21:35:04.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:04.9|Info|InitialSetup|Starting migration of Log DB to 1
2025-05-25 21:35:04.9|Info|FluentMigrator.Runner.MigrationRunner|CreateTable Logs
2025-05-25 21:35:05.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|CREATE TABLE "Logs" ("Id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT, "Message" TEXT NOT NULL, "Time" DATETIME NOT NULL, "Logger" TEXT NOT NULL, "Exception" TEXT, "ExceptionType" TEXT, "Level" TEXT NOT NULL)
2025-05-25 21:35:05.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0098258s
2025-05-25 21:35:05.0|Info|FluentMigrator.Runner.MigrationRunner|CreateIndex Logs (Time)
2025-05-25 21:35:05.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|CREATE INDEX "IX_Logs_Time" ON "Logs" ("Time" ASC)
2025-05-25 21:35:05.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0091513s
2025-05-25 21:35:05.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (1, '2025-05-25T19:35:05', 'InitialSetup')
2025-05-25 21:35:05.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:05.0|Info|FluentMigrator.Runner.MigrationRunner|1: InitialSetup migrated
2025-05-25 21:35:05.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0380846s
2025-05-25 21:35:05.0|Info|FluentMigrator.Runner.MigrationRunner|104: add_moviefiles_table migrating
2025-05-25 21:35:05.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:05.1|Info|add_moviefiles_table|Starting migration of Log DB to 104
2025-05-25 21:35:05.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (104, '2025-05-25T19:35:05', 'add_moviefiles_table')
2025-05-25 21:35:05.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:05.1|Info|FluentMigrator.Runner.MigrationRunner|104: add_moviefiles_table migrated
2025-05-25 21:35:05.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0372913s
2025-05-25 21:35:05.1|Info|FluentMigrator.Runner.MigrationRunner|105: fix_history_movieId migrating
2025-05-25 21:35:05.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:05.1|Info|fix_history_movieId|Starting migration of Log DB to 105
2025-05-25 21:35:05.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (105, '2025-05-25T19:35:05', 'fix_history_movieId')
2025-05-25 21:35:05.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:05.2|Info|FluentMigrator.Runner.MigrationRunner|105: fix_history_movieId migrated
2025-05-25 21:35:05.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0385445s
2025-05-25 21:35:05.2|Info|FluentMigrator.Runner.MigrationRunner|106: add_tmdb_stuff migrating
2025-05-25 21:35:05.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:05.2|Info|add_tmdb_stuff|Starting migration of Log DB to 106
2025-05-25 21:35:05.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (106, '2025-05-25T19:35:05', 'add_tmdb_stuff')
2025-05-25 21:35:05.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:05.2|Info|FluentMigrator.Runner.MigrationRunner|106: add_tmdb_stuff migrated
2025-05-25 21:35:05.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0410166s
2025-05-25 21:35:05.2|Info|FluentMigrator.Runner.MigrationRunner|107: fix_movie_files migrating
2025-05-25 21:35:05.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:05.3|Info|fix_movie_files|Starting migration of Log DB to 107
2025-05-25 21:35:05.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (107, '2025-05-25T19:35:05', 'fix_movie_files')
2025-05-25 21:35:05.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:05.3|Info|FluentMigrator.Runner.MigrationRunner|107: fix_movie_files migrated
2025-05-25 21:35:05.3|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0369042s
2025-05-25 21:35:05.3|Info|FluentMigrator.Runner.MigrationRunner|108: update_schedule_intervale migrating
2025-05-25 21:35:05.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:05.3|Info|update_schedule_intervale|Starting migration of Log DB to 108
2025-05-25 21:35:05.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (108, '2025-05-25T19:35:05', 'update_schedule_intervale')
2025-05-25 21:35:05.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:05.3|Info|FluentMigrator.Runner.MigrationRunner|108: update_schedule_intervale migrated
2025-05-25 21:35:05.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0368862s
2025-05-25 21:35:05.4|Info|FluentMigrator.Runner.MigrationRunner|109: add_movie_formats_to_naming_config migrating
2025-05-25 21:35:05.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:05.4|Info|add_movie_formats_to_naming_config|Starting migration of Log DB to 109
2025-05-25 21:35:05.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (109, '2025-05-25T19:35:05', 'add_movie_formats_to_naming_config')
2025-05-25 21:35:05.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:05.4|Info|FluentMigrator.Runner.MigrationRunner|109: add_movie_formats_to_naming_config migrated
2025-05-25 21:35:05.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0365944s
2025-05-25 21:35:05.4|Info|FluentMigrator.Runner.MigrationRunner|110: add_phyiscal_release migrating
2025-05-25 21:35:05.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:05.4|Info|add_phyiscal_release|Starting migration of Log DB to 110
2025-05-25 21:35:05.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (110, '2025-05-25T19:35:05', 'add_phyiscal_release')
2025-05-25 21:35:05.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:05.5|Info|FluentMigrator.Runner.MigrationRunner|110: add_phyiscal_release migrated
2025-05-25 21:35:05.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0405375s
2025-05-25 21:35:05.5|Info|FluentMigrator.Runner.MigrationRunner|111: remove_bitmetv migrating
2025-05-25 21:35:05.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:05.5|Info|remove_bitmetv|Starting migration of Log DB to 111
2025-05-25 21:35:05.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (111, '2025-05-25T19:35:05', 'remove_bitmetv')
2025-05-25 21:35:05.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:05.5|Info|FluentMigrator.Runner.MigrationRunner|111: remove_bitmetv migrated
2025-05-25 21:35:05.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0366662s
2025-05-25 21:35:05.5|Info|FluentMigrator.Runner.MigrationRunner|112: remove_torrentleech migrating
2025-05-25 21:35:05.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:05.6|Info|remove_torrentleech|Starting migration of Log DB to 112
2025-05-25 21:35:05.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (112, '2025-05-25T19:35:05', 'remove_torrentleech')
2025-05-25 21:35:05.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:05.6|Info|FluentMigrator.Runner.MigrationRunner|112: remove_torrentleech migrated
2025-05-25 21:35:05.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0367827s
2025-05-25 21:35:05.6|Info|FluentMigrator.Runner.MigrationRunner|113: remove_broadcasthenet migrating
2025-05-25 21:35:05.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:05.6|Info|remove_broadcasthenet|Starting migration of Log DB to 113
2025-05-25 21:35:05.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (113, '2025-05-25T19:35:05', 'remove_broadcasthenet')
2025-05-25 21:35:05.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:05.7|Info|FluentMigrator.Runner.MigrationRunner|113: remove_broadcasthenet migrated
2025-05-25 21:35:05.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0369062s
2025-05-25 21:35:05.7|Info|FluentMigrator.Runner.MigrationRunner|114: remove_fanzub migrating
2025-05-25 21:35:05.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:05.7|Info|remove_fanzub|Starting migration of Log DB to 114
2025-05-25 21:35:05.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (114, '2025-05-25T19:35:05', 'remove_fanzub')
2025-05-25 21:35:05.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:05.7|Info|FluentMigrator.Runner.MigrationRunner|114: remove_fanzub migrated
2025-05-25 21:35:05.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0379623s
2025-05-25 21:35:05.7|Info|FluentMigrator.Runner.MigrationRunner|115: update_movie_sorttitle migrating
2025-05-25 21:35:05.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:05.8|Info|update_movie_sorttitle|Starting migration of Log DB to 115
2025-05-25 21:35:05.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (115, '2025-05-25T19:35:05', 'update_movie_sorttitle')
2025-05-25 21:35:05.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:05.8|Info|FluentMigrator.Runner.MigrationRunner|115: update_movie_sorttitle migrated
2025-05-25 21:35:05.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0380006s
2025-05-25 21:35:05.8|Info|FluentMigrator.Runner.MigrationRunner|116: update_movie_sorttitle_again migrating
2025-05-25 21:35:05.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:05.8|Info|update_movie_sorttitle_again|Starting migration of Log DB to 116
2025-05-25 21:35:05.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (116, '2025-05-25T19:35:05', 'update_movie_sorttitle_again')
2025-05-25 21:35:05.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:05.9|Info|FluentMigrator.Runner.MigrationRunner|116: update_movie_sorttitle_again migrated
2025-05-25 21:35:05.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0364094s
2025-05-25 21:35:05.9|Info|FluentMigrator.Runner.MigrationRunner|117: update_movie_file migrating
2025-05-25 21:35:05.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:05.9|Info|update_movie_file|Starting migration of Log DB to 117
2025-05-25 21:35:05.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (117, '2025-05-25T19:35:05', 'update_movie_file')
2025-05-25 21:35:05.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:05.9|Info|FluentMigrator.Runner.MigrationRunner|117: update_movie_file migrated
2025-05-25 21:35:05.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0369519s
2025-05-25 21:35:05.9|Info|FluentMigrator.Runner.MigrationRunner|118: update_movie_slug migrating
2025-05-25 21:35:05.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:06.0|Info|update_movie_slug|Starting migration of Log DB to 118
2025-05-25 21:35:06.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (118, '2025-05-25T19:35:06', 'update_movie_slug')
2025-05-25 21:35:06.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:06.0|Info|FluentMigrator.Runner.MigrationRunner|118: update_movie_slug migrated
2025-05-25 21:35:06.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0424985s
2025-05-25 21:35:06.0|Info|FluentMigrator.Runner.MigrationRunner|119: add_youtube_trailer_id migrating
2025-05-25 21:35:06.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:06.0|Info|add_youtube_trailer_id|Starting migration of Log DB to 119
2025-05-25 21:35:06.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (119, '2025-05-25T19:35:06', 'add_youtube_trailer_id')
2025-05-25 21:35:06.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:06.1|Info|FluentMigrator.Runner.MigrationRunner|119: add_youtube_trailer_id migrated
2025-05-25 21:35:06.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0379048s
2025-05-25 21:35:06.1|Info|FluentMigrator.Runner.MigrationRunner|120: add_studio migrating
2025-05-25 21:35:06.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:06.1|Info|add_studio|Starting migration of Log DB to 120
2025-05-25 21:35:06.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (120, '2025-05-25T19:35:06', 'add_studio')
2025-05-25 21:35:06.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:06.1|Info|FluentMigrator.Runner.MigrationRunner|120: add_studio migrated
2025-05-25 21:35:06.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0361066s
2025-05-25 21:35:06.1|Info|FluentMigrator.Runner.MigrationRunner|121: update_filedate_config migrating
2025-05-25 21:35:06.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:06.1|Info|update_filedate_config|Starting migration of Log DB to 121
2025-05-25 21:35:06.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (121, '2025-05-25T19:35:06', 'update_filedate_config')
2025-05-25 21:35:06.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:06.2|Info|FluentMigrator.Runner.MigrationRunner|121: update_filedate_config migrated
2025-05-25 21:35:06.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0387857s
2025-05-25 21:35:06.2|Info|FluentMigrator.Runner.MigrationRunner|122: add_movieid_to_blacklist migrating
2025-05-25 21:35:06.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:06.2|Info|add_movieid_to_blacklist|Starting migration of Log DB to 122
2025-05-25 21:35:06.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (122, '2025-05-25T19:35:06', 'add_movieid_to_blacklist')
2025-05-25 21:35:06.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:06.2|Info|FluentMigrator.Runner.MigrationRunner|122: add_movieid_to_blacklist migrated
2025-05-25 21:35:06.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0379907s
2025-05-25 21:35:06.3|Info|FluentMigrator.Runner.MigrationRunner|123: create_netimport_table migrating
2025-05-25 21:35:06.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:06.3|Info|create_netimport_table|Starting migration of Log DB to 123
2025-05-25 21:35:06.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (123, '2025-05-25T19:35:06', 'create_netimport_table')
2025-05-25 21:35:06.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:06.3|Info|FluentMigrator.Runner.MigrationRunner|123: create_netimport_table migrated
2025-05-25 21:35:06.3|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0370492s
2025-05-25 21:35:06.3|Info|FluentMigrator.Runner.MigrationRunner|124: add_preferred_tags_to_profile migrating
2025-05-25 21:35:06.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:06.3|Info|add_preferred_tags_to_profile|Starting migration of Log DB to 124
2025-05-25 21:35:06.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (124, '2025-05-25T19:35:06', 'add_preferred_tags_to_profile')
2025-05-25 21:35:06.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:06.4|Info|FluentMigrator.Runner.MigrationRunner|124: add_preferred_tags_to_profile migrated
2025-05-25 21:35:06.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.037104s
2025-05-25 21:35:06.4|Info|FluentMigrator.Runner.MigrationRunner|125: fix_imdb_unique migrating
2025-05-25 21:35:06.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:06.4|Info|fix_imdb_unique|Starting migration of Log DB to 125
2025-05-25 21:35:06.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (125, '2025-05-25T19:35:06', 'fix_imdb_unique')
2025-05-25 21:35:06.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:06.4|Info|FluentMigrator.Runner.MigrationRunner|125: fix_imdb_unique migrated
2025-05-25 21:35:06.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0373077s
2025-05-25 21:35:06.4|Info|FluentMigrator.Runner.MigrationRunner|126: update_qualities_and_profiles migrating
2025-05-25 21:35:06.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:06.5|Info|update_qualities_and_profiles|Starting migration of Log DB to 126
2025-05-25 21:35:06.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (126, '2025-05-25T19:35:06', 'update_qualities_and_profiles')
2025-05-25 21:35:06.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:06.5|Info|FluentMigrator.Runner.MigrationRunner|126: update_qualities_and_profiles migrated
2025-05-25 21:35:06.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0375787s
2025-05-25 21:35:06.5|Info|FluentMigrator.Runner.MigrationRunner|127: remove_wombles migrating
2025-05-25 21:35:06.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:06.5|Info|remove_wombles|Starting migration of Log DB to 127
2025-05-25 21:35:06.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (127, '2025-05-25T19:35:06', 'remove_wombles')
2025-05-25 21:35:06.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:06.6|Info|FluentMigrator.Runner.MigrationRunner|127: remove_wombles migrated
2025-05-25 21:35:06.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0369365s
2025-05-25 21:35:06.6|Info|FluentMigrator.Runner.MigrationRunner|128: remove_kickass migrating
2025-05-25 21:35:06.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:06.6|Info|remove_kickass|Starting migration of Log DB to 128
2025-05-25 21:35:06.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (128, '2025-05-25T19:35:06', 'remove_kickass')
2025-05-25 21:35:06.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:06.6|Info|FluentMigrator.Runner.MigrationRunner|128: remove_kickass migrated
2025-05-25 21:35:06.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0364901s
2025-05-25 21:35:06.6|Info|FluentMigrator.Runner.MigrationRunner|129: add_parsed_movie_info_to_pending_release migrating
2025-05-25 21:35:06.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:06.7|Info|add_parsed_movie_info_to_pending_release|Starting migration of Log DB to 129
2025-05-25 21:35:06.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (129, '2025-05-25T19:35:06', 'add_parsed_movie_info_to_pending_release')
2025-05-25 21:35:06.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:06.7|Info|FluentMigrator.Runner.MigrationRunner|129: add_parsed_movie_info_to_pending_release migrated
2025-05-25 21:35:06.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.036884s
2025-05-25 21:35:06.7|Info|FluentMigrator.Runner.MigrationRunner|130: remove_wombles_kickass migrating
2025-05-25 21:35:06.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:06.7|Info|remove_wombles_kickass|Starting migration of Log DB to 130
2025-05-25 21:35:06.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (130, '2025-05-25T19:35:06', 'remove_wombles_kickass')
2025-05-25 21:35:06.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:06.8|Info|FluentMigrator.Runner.MigrationRunner|130: remove_wombles_kickass migrated
2025-05-25 21:35:06.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0364331s
2025-05-25 21:35:06.8|Info|FluentMigrator.Runner.MigrationRunner|131: make_parsed_episode_info_nullable migrating
2025-05-25 21:35:06.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:06.8|Info|make_parsed_episode_info_nullable|Starting migration of Log DB to 131
2025-05-25 21:35:06.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (131, '2025-05-25T19:35:06', 'make_parsed_episode_info_nullable')
2025-05-25 21:35:06.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:06.8|Info|FluentMigrator.Runner.MigrationRunner|131: make_parsed_episode_info_nullable migrated
2025-05-25 21:35:06.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.038335s
2025-05-25 21:35:06.8|Info|FluentMigrator.Runner.MigrationRunner|132: rename_torrent_downloadstation migrating
2025-05-25 21:35:06.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:06.9|Info|rename_torrent_downloadstation|Starting migration of Log DB to 132
2025-05-25 21:35:06.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (132, '2025-05-25T19:35:06', 'rename_torrent_downloadstation')
2025-05-25 21:35:06.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:06.9|Info|FluentMigrator.Runner.MigrationRunner|132: rename_torrent_downloadstation migrated
2025-05-25 21:35:06.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0381275s
2025-05-25 21:35:06.9|Info|FluentMigrator.Runner.MigrationRunner|133: add_minimumavailability migrating
2025-05-25 21:35:06.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:06.9|Info|add_minimumavailability|Starting migration of Log DB to 133
2025-05-25 21:35:06.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (133, '2025-05-25T19:35:06', 'add_minimumavailability')
2025-05-25 21:35:06.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:06.9|Info|FluentMigrator.Runner.MigrationRunner|133: add_minimumavailability migrated
2025-05-25 21:35:07.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0393712s
2025-05-25 21:35:07.0|Info|FluentMigrator.Runner.MigrationRunner|134: add_remux_qualities_for_the_wankers migrating
2025-05-25 21:35:07.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:07.0|Info|add_remux_qualities_for_the_wankers|Starting migration of Log DB to 134
2025-05-25 21:35:07.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (134, '2025-05-25T19:35:07', 'add_remux_qualities_for_the_wankers')
2025-05-25 21:35:07.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:07.0|Info|FluentMigrator.Runner.MigrationRunner|134: add_remux_qualities_for_the_wankers migrated
2025-05-25 21:35:07.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0386269s
2025-05-25 21:35:07.0|Info|FluentMigrator.Runner.MigrationRunner|135: add_haspredbentry_to_movies migrating
2025-05-25 21:35:07.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:07.1|Info|add_haspredbentry_to_movies|Starting migration of Log DB to 135
2025-05-25 21:35:07.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (135, '2025-05-25T19:35:07', 'add_haspredbentry_to_movies')
2025-05-25 21:35:07.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:07.1|Info|FluentMigrator.Runner.MigrationRunner|135: add_haspredbentry_to_movies migrated
2025-05-25 21:35:07.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.036656s
2025-05-25 21:35:07.1|Info|FluentMigrator.Runner.MigrationRunner|136: add_pathstate_to_movies migrating
2025-05-25 21:35:07.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:07.1|Info|add_pathstate_to_movies|Starting migration of Log DB to 136
2025-05-25 21:35:07.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (136, '2025-05-25T19:35:07', 'add_pathstate_to_movies')
2025-05-25 21:35:07.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:07.1|Info|FluentMigrator.Runner.MigrationRunner|136: add_pathstate_to_movies migrated
2025-05-25 21:35:07.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0363666s
2025-05-25 21:35:07.2|Info|FluentMigrator.Runner.MigrationRunner|137: add_import_exclusions_table migrating
2025-05-25 21:35:07.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:07.2|Info|add_import_exclusions_table|Starting migration of Log DB to 137
2025-05-25 21:35:07.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (137, '2025-05-25T19:35:07', 'add_import_exclusions_table')
2025-05-25 21:35:07.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:07.2|Info|FluentMigrator.Runner.MigrationRunner|137: add_import_exclusions_table migrated
2025-05-25 21:35:07.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.036342s
2025-05-25 21:35:07.2|Info|FluentMigrator.Runner.MigrationRunner|138: add_physical_release_note migrating
2025-05-25 21:35:07.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:07.2|Info|add_physical_release_note|Starting migration of Log DB to 138
2025-05-25 21:35:07.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (138, '2025-05-25T19:35:07', 'add_physical_release_note')
2025-05-25 21:35:07.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:07.3|Info|FluentMigrator.Runner.MigrationRunner|138: add_physical_release_note migrated
2025-05-25 21:35:07.3|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0382683s
2025-05-25 21:35:07.3|Info|FluentMigrator.Runner.MigrationRunner|139: consolidate_indexer_baseurl migrating
2025-05-25 21:35:07.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:07.3|Info|consolidate_indexer_baseurl|Starting migration of Log DB to 139
2025-05-25 21:35:07.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (139, '2025-05-25T19:35:07', 'consolidate_indexer_baseurl')
2025-05-25 21:35:07.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:07.3|Info|FluentMigrator.Runner.MigrationRunner|139: consolidate_indexer_baseurl migrated
2025-05-25 21:35:07.3|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0400628s
2025-05-25 21:35:07.4|Info|FluentMigrator.Runner.MigrationRunner|140: add_alternative_titles_table migrating
2025-05-25 21:35:07.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:07.4|Info|add_alternative_titles_table|Starting migration of Log DB to 140
2025-05-25 21:35:07.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (140, '2025-05-25T19:35:07', 'add_alternative_titles_table')
2025-05-25 21:35:07.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:07.4|Info|FluentMigrator.Runner.MigrationRunner|140: add_alternative_titles_table migrated
2025-05-25 21:35:07.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0377264s
2025-05-25 21:35:07.4|Info|FluentMigrator.Runner.MigrationRunner|141: fix_duplicate_alt_titles migrating
2025-05-25 21:35:07.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:07.4|Info|fix_duplicate_alt_titles|Starting migration of Log DB to 141
2025-05-25 21:35:07.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (141, '2025-05-25T19:35:07', 'fix_duplicate_alt_titles')
2025-05-25 21:35:07.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:07.5|Info|FluentMigrator.Runner.MigrationRunner|141: fix_duplicate_alt_titles migrated
2025-05-25 21:35:07.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0387204s
2025-05-25 21:35:07.5|Info|FluentMigrator.Runner.MigrationRunner|142: movie_extras migrating
2025-05-25 21:35:07.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:07.5|Info|movie_extras|Starting migration of Log DB to 142
2025-05-25 21:35:07.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (142, '2025-05-25T19:35:07', 'movie_extras')
2025-05-25 21:35:07.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:07.5|Info|FluentMigrator.Runner.MigrationRunner|142: movie_extras migrated
2025-05-25 21:35:07.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0368287s
2025-05-25 21:35:07.5|Info|FluentMigrator.Runner.MigrationRunner|143: clean_core_tv migrating
2025-05-25 21:35:07.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:07.6|Info|clean_core_tv|Starting migration of Log DB to 143
2025-05-25 21:35:07.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (143, '2025-05-25T19:35:07', 'clean_core_tv')
2025-05-25 21:35:07.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:07.6|Info|FluentMigrator.Runner.MigrationRunner|143: clean_core_tv migrated
2025-05-25 21:35:07.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0370913s
2025-05-25 21:35:07.6|Info|FluentMigrator.Runner.MigrationRunner|144: add_cookies_to_indexer_status migrating
2025-05-25 21:35:07.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:07.6|Info|add_cookies_to_indexer_status|Starting migration of Log DB to 144
2025-05-25 21:35:07.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (144, '2025-05-25T19:35:07', 'add_cookies_to_indexer_status')
2025-05-25 21:35:07.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:07.6|Info|FluentMigrator.Runner.MigrationRunner|144: add_cookies_to_indexer_status migrated
2025-05-25 21:35:07.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0365035s
2025-05-25 21:35:07.7|Info|FluentMigrator.Runner.MigrationRunner|145: banner_to_fanart migrating
2025-05-25 21:35:07.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:07.7|Info|banner_to_fanart|Starting migration of Log DB to 145
2025-05-25 21:35:07.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (145, '2025-05-25T19:35:07', 'banner_to_fanart')
2025-05-25 21:35:07.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:07.7|Info|FluentMigrator.Runner.MigrationRunner|145: banner_to_fanart migrated
2025-05-25 21:35:07.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0367157s
2025-05-25 21:35:07.7|Info|FluentMigrator.Runner.MigrationRunner|146: naming_config_colon_action migrating
2025-05-25 21:35:07.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:07.7|Info|naming_config_colon_action|Starting migration of Log DB to 146
2025-05-25 21:35:07.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (146, '2025-05-25T19:35:07', 'naming_config_colon_action')
2025-05-25 21:35:07.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:07.8|Info|FluentMigrator.Runner.MigrationRunner|146: naming_config_colon_action migrated
2025-05-25 21:35:07.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0369131s
2025-05-25 21:35:07.8|Info|FluentMigrator.Runner.MigrationRunner|147: add_custom_formats migrating
2025-05-25 21:35:07.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:07.8|Info|add_custom_formats|Starting migration of Log DB to 147
2025-05-25 21:35:07.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (147, '2025-05-25T19:35:07', 'add_custom_formats')
2025-05-25 21:35:07.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:07.8|Info|FluentMigrator.Runner.MigrationRunner|147: add_custom_formats migrated
2025-05-25 21:35:07.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0370191s
2025-05-25 21:35:07.9|Info|FluentMigrator.Runner.MigrationRunner|148: remove_extra_naming_config migrating
2025-05-25 21:35:07.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:07.9|Info|remove_extra_naming_config|Starting migration of Log DB to 148
2025-05-25 21:35:07.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (148, '2025-05-25T19:35:07', 'remove_extra_naming_config')
2025-05-25 21:35:07.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:07.9|Info|FluentMigrator.Runner.MigrationRunner|148: remove_extra_naming_config migrated
2025-05-25 21:35:07.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0366769s
2025-05-25 21:35:07.9|Info|FluentMigrator.Runner.MigrationRunner|149: convert_regex_required_tags migrating
2025-05-25 21:35:07.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:08.0|Info|convert_regex_required_tags|Starting migration of Log DB to 149
2025-05-25 21:35:08.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (149, '2025-05-25T19:35:08', 'convert_regex_required_tags')
2025-05-25 21:35:08.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:08.0|Info|FluentMigrator.Runner.MigrationRunner|149: convert_regex_required_tags migrated
2025-05-25 21:35:08.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0590123s
2025-05-25 21:35:08.0|Info|FluentMigrator.Runner.MigrationRunner|150: fix_format_tags_double_underscore migrating
2025-05-25 21:35:08.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:08.0|Info|fix_format_tags_double_underscore|Starting migration of Log DB to 150
2025-05-25 21:35:08.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (150, '2025-05-25T19:35:08', 'fix_format_tags_double_underscore')
2025-05-25 21:35:08.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:08.1|Info|FluentMigrator.Runner.MigrationRunner|150: fix_format_tags_double_underscore migrated
2025-05-25 21:35:08.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0415343s
2025-05-25 21:35:08.1|Info|FluentMigrator.Runner.MigrationRunner|151: add_tags_to_net_import migrating
2025-05-25 21:35:08.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:08.1|Info|add_tags_to_net_import|Starting migration of Log DB to 151
2025-05-25 21:35:08.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (151, '2025-05-25T19:35:08', 'add_tags_to_net_import')
2025-05-25 21:35:08.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:08.1|Info|FluentMigrator.Runner.MigrationRunner|151: add_tags_to_net_import migrated
2025-05-25 21:35:08.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0380377s
2025-05-25 21:35:08.1|Info|FluentMigrator.Runner.MigrationRunner|152: add_custom_filters migrating
2025-05-25 21:35:08.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:08.2|Info|add_custom_filters|Starting migration of Log DB to 152
2025-05-25 21:35:08.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (152, '2025-05-25T19:35:08', 'add_custom_filters')
2025-05-25 21:35:08.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:08.2|Info|FluentMigrator.Runner.MigrationRunner|152: add_custom_filters migrated
2025-05-25 21:35:08.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0382013s
2025-05-25 21:35:08.2|Info|FluentMigrator.Runner.MigrationRunner|153: indexer_client_status_search_changes migrating
2025-05-25 21:35:08.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:08.2|Info|indexer_client_status_search_changes|Starting migration of Log DB to 153
2025-05-25 21:35:08.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (153, '2025-05-25T19:35:08', 'indexer_client_status_search_changes')
2025-05-25 21:35:08.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:08.3|Info|FluentMigrator.Runner.MigrationRunner|153: indexer_client_status_search_changes migrated
2025-05-25 21:35:08.3|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0386364s
2025-05-25 21:35:08.3|Info|FluentMigrator.Runner.MigrationRunner|154: add_language_to_files_history_blacklist migrating
2025-05-25 21:35:08.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:08.3|Info|add_language_to_files_history_blacklist|Starting migration of Log DB to 154
2025-05-25 21:35:08.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (154, '2025-05-25T19:35:08', 'add_language_to_files_history_blacklist')
2025-05-25 21:35:08.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:08.3|Info|FluentMigrator.Runner.MigrationRunner|154: add_language_to_files_history_blacklist migrated
2025-05-25 21:35:08.3|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0366922s
2025-05-25 21:35:08.3|Info|FluentMigrator.Runner.MigrationRunner|155: add_update_allowed_quality_profile migrating
2025-05-25 21:35:08.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:08.3|Info|add_update_allowed_quality_profile|Starting migration of Log DB to 155
2025-05-25 21:35:08.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (155, '2025-05-25T19:35:08', 'add_update_allowed_quality_profile')
2025-05-25 21:35:08.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:08.4|Info|FluentMigrator.Runner.MigrationRunner|155: add_update_allowed_quality_profile migrated
2025-05-25 21:35:08.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0368306s
2025-05-25 21:35:08.4|Info|FluentMigrator.Runner.MigrationRunner|156: add_download_client_priority migrating
2025-05-25 21:35:08.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:08.4|Info|add_download_client_priority|Starting migration of Log DB to 156
2025-05-25 21:35:08.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (156, '2025-05-25T19:35:08', 'add_download_client_priority')
2025-05-25 21:35:08.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:08.4|Info|FluentMigrator.Runner.MigrationRunner|156: add_download_client_priority migrated
2025-05-25 21:35:08.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0375603s
2025-05-25 21:35:08.5|Info|FluentMigrator.Runner.MigrationRunner|157: remove_growl_prowl migrating
2025-05-25 21:35:08.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:08.5|Info|remove_growl_prowl|Starting migration of Log DB to 157
2025-05-25 21:35:08.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (157, '2025-05-25T19:35:08', 'remove_growl_prowl')
2025-05-25 21:35:08.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:08.5|Info|FluentMigrator.Runner.MigrationRunner|157: remove_growl_prowl migrated
2025-05-25 21:35:08.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0506722s
2025-05-25 21:35:08.5|Info|FluentMigrator.Runner.MigrationRunner|158: remove_plex_hometheatre migrating
2025-05-25 21:35:08.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:08.6|Info|remove_plex_hometheatre|Starting migration of Log DB to 158
2025-05-25 21:35:08.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (158, '2025-05-25T19:35:08', 'remove_plex_hometheatre')
2025-05-25 21:35:08.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:08.6|Info|FluentMigrator.Runner.MigrationRunner|158: remove_plex_hometheatre migrated
2025-05-25 21:35:08.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0574242s
2025-05-25 21:35:08.6|Info|FluentMigrator.Runner.MigrationRunner|159: add_webrip_qualites migrating
2025-05-25 21:35:08.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:08.7|Info|add_webrip_qualites|Starting migration of Log DB to 159
2025-05-25 21:35:08.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (159, '2025-05-25T19:35:08', 'add_webrip_qualites')
2025-05-25 21:35:08.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:08.7|Info|FluentMigrator.Runner.MigrationRunner|159: add_webrip_qualites migrated
2025-05-25 21:35:08.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0386624s
2025-05-25 21:35:08.7|Info|FluentMigrator.Runner.MigrationRunner|160: health_issue_notification migrating
2025-05-25 21:35:08.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:08.7|Info|health_issue_notification|Starting migration of Log DB to 160
2025-05-25 21:35:08.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (160, '2025-05-25T19:35:08', 'health_issue_notification')
2025-05-25 21:35:08.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:08.7|Info|FluentMigrator.Runner.MigrationRunner|160: health_issue_notification migrated
2025-05-25 21:35:08.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.038309s
2025-05-25 21:35:08.8|Info|FluentMigrator.Runner.MigrationRunner|161: speed_improvements migrating
2025-05-25 21:35:08.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:08.8|Info|speed_improvements|Starting migration of Log DB to 161
2025-05-25 21:35:08.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (161, '2025-05-25T19:35:08', 'speed_improvements')
2025-05-25 21:35:08.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:08.8|Info|FluentMigrator.Runner.MigrationRunner|161: speed_improvements migrated
2025-05-25 21:35:08.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0376436s
2025-05-25 21:35:08.8|Info|FluentMigrator.Runner.MigrationRunner|162: fix_profile_format_default migrating
2025-05-25 21:35:08.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:08.8|Info|fix_profile_format_default|Starting migration of Log DB to 162
2025-05-25 21:35:08.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (162, '2025-05-25T19:35:08', 'fix_profile_format_default')
2025-05-25 21:35:08.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:08.9|Info|FluentMigrator.Runner.MigrationRunner|162: fix_profile_format_default migrated
2025-05-25 21:35:08.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0372539s
2025-05-25 21:35:08.9|Info|FluentMigrator.Runner.MigrationRunner|163: task_duration migrating
2025-05-25 21:35:08.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:08.9|Info|task_duration|Starting migration of Log DB to 163
2025-05-25 21:35:08.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (163, '2025-05-25T19:35:08', 'task_duration')
2025-05-25 21:35:08.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:08.9|Info|FluentMigrator.Runner.MigrationRunner|163: task_duration migrated
2025-05-25 21:35:08.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0398502s
2025-05-25 21:35:08.9|Info|FluentMigrator.Runner.MigrationRunner|164: movie_collections_crew migrating
2025-05-25 21:35:09.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:09.0|Info|movie_collections_crew|Starting migration of Log DB to 164
2025-05-25 21:35:09.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (164, '2025-05-25T19:35:09', 'movie_collections_crew')
2025-05-25 21:35:09.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:09.0|Info|FluentMigrator.Runner.MigrationRunner|164: movie_collections_crew migrated
2025-05-25 21:35:09.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0391886s
2025-05-25 21:35:09.0|Info|FluentMigrator.Runner.MigrationRunner|165: remove_custom_formats_from_quality_model migrating
2025-05-25 21:35:09.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:09.0|Info|remove_custom_formats_from_quality_model|Starting migration of Log DB to 165
2025-05-25 21:35:09.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (165, '2025-05-25T19:35:09', 'remove_custom_formats_from_quality_model')
2025-05-25 21:35:09.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:09.1|Info|FluentMigrator.Runner.MigrationRunner|165: remove_custom_formats_from_quality_model migrated
2025-05-25 21:35:09.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.037742s
2025-05-25 21:35:09.1|Info|FluentMigrator.Runner.MigrationRunner|166: fix_tmdb_list_config migrating
2025-05-25 21:35:09.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:09.1|Info|fix_tmdb_list_config|Starting migration of Log DB to 166
2025-05-25 21:35:09.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (166, '2025-05-25T19:35:09', 'fix_tmdb_list_config')
2025-05-25 21:35:09.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:09.1|Info|FluentMigrator.Runner.MigrationRunner|166: fix_tmdb_list_config migrated
2025-05-25 21:35:09.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0391464s
2025-05-25 21:35:09.2|Info|FluentMigrator.Runner.MigrationRunner|167: remove_movie_pathstate migrating
2025-05-25 21:35:09.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:09.2|Info|remove_movie_pathstate|Starting migration of Log DB to 167
2025-05-25 21:35:09.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (167, '2025-05-25T19:35:09', 'remove_movie_pathstate')
2025-05-25 21:35:09.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:09.2|Info|FluentMigrator.Runner.MigrationRunner|167: remove_movie_pathstate migrated
2025-05-25 21:35:09.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0399803s
2025-05-25 21:35:09.2|Info|FluentMigrator.Runner.MigrationRunner|168: custom_format_rework migrating
2025-05-25 21:35:09.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:09.2|Info|custom_format_rework|Starting migration of Log DB to 168
2025-05-25 21:35:09.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (168, '2025-05-25T19:35:09', 'custom_format_rework')
2025-05-25 21:35:09.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:09.3|Info|FluentMigrator.Runner.MigrationRunner|168: custom_format_rework migrated
2025-05-25 21:35:09.3|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0381806s
2025-05-25 21:35:09.3|Info|FluentMigrator.Runner.MigrationRunner|169: custom_format_scores migrating
2025-05-25 21:35:09.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:09.3|Info|custom_format_scores|Starting migration of Log DB to 169
2025-05-25 21:35:09.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (169, '2025-05-25T19:35:09', 'custom_format_scores')
2025-05-25 21:35:09.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:09.3|Info|FluentMigrator.Runner.MigrationRunner|169: custom_format_scores migrated
2025-05-25 21:35:09.3|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0366995s
2025-05-25 21:35:09.3|Info|FluentMigrator.Runner.MigrationRunner|170: fix_trakt_list_config migrating
2025-05-25 21:35:09.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:09.4|Info|fix_trakt_list_config|Starting migration of Log DB to 170
2025-05-25 21:35:09.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (170, '2025-05-25T19:35:09', 'fix_trakt_list_config')
2025-05-25 21:35:09.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:09.4|Info|FluentMigrator.Runner.MigrationRunner|170: fix_trakt_list_config migrated
2025-05-25 21:35:09.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0370288s
2025-05-25 21:35:09.4|Info|FluentMigrator.Runner.MigrationRunner|171: quality_definition_preferred_size migrating
2025-05-25 21:35:09.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:09.4|Info|quality_definition_preferred_size|Starting migration of Log DB to 171
2025-05-25 21:35:09.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (171, '2025-05-25T19:35:09', 'quality_definition_preferred_size')
2025-05-25 21:35:09.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:09.5|Info|FluentMigrator.Runner.MigrationRunner|171: quality_definition_preferred_size migrated
2025-05-25 21:35:09.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0392582s
2025-05-25 21:35:09.5|Info|FluentMigrator.Runner.MigrationRunner|172: add_download_history migrating
2025-05-25 21:35:09.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:09.5|Info|add_download_history|Starting migration of Log DB to 172
2025-05-25 21:35:09.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (172, '2025-05-25T19:35:09', 'add_download_history')
2025-05-25 21:35:09.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:09.5|Info|FluentMigrator.Runner.MigrationRunner|172: add_download_history migrated
2025-05-25 21:35:09.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0371701s
2025-05-25 21:35:09.5|Info|FluentMigrator.Runner.MigrationRunner|173: net_import_status migrating
2025-05-25 21:35:09.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:09.6|Info|net_import_status|Starting migration of Log DB to 173
2025-05-25 21:35:09.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (173, '2025-05-25T19:35:09', 'net_import_status')
2025-05-25 21:35:09.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:09.6|Info|FluentMigrator.Runner.MigrationRunner|173: net_import_status migrated
2025-05-25 21:35:09.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0393453s
2025-05-25 21:35:09.6|Info|FluentMigrator.Runner.MigrationRunner|174: email_multiple_addresses migrating
2025-05-25 21:35:09.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:09.6|Info|email_multiple_addresses|Starting migration of Log DB to 174
2025-05-25 21:35:09.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (174, '2025-05-25T19:35:09', 'email_multiple_addresses')
2025-05-25 21:35:09.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:09.6|Info|FluentMigrator.Runner.MigrationRunner|174: email_multiple_addresses migrated
2025-05-25 21:35:09.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0368047s
2025-05-25 21:35:09.7|Info|FluentMigrator.Runner.MigrationRunner|175: remove_chown_and_folderchmod_config migrating
2025-05-25 21:35:09.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:09.7|Info|remove_chown_and_folderchmod_config|Starting migration of Log DB to 175
2025-05-25 21:35:09.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (175, '2025-05-25T19:35:09', 'remove_chown_and_folderchmod_config')
2025-05-25 21:35:09.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:09.7|Info|FluentMigrator.Runner.MigrationRunner|175: remove_chown_and_folderchmod_config migrated
2025-05-25 21:35:09.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0374421s
2025-05-25 21:35:09.7|Info|FluentMigrator.Runner.MigrationRunner|176: movie_recommendations migrating
2025-05-25 21:35:09.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:09.7|Info|movie_recommendations|Starting migration of Log DB to 176
2025-05-25 21:35:09.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (176, '2025-05-25T19:35:09', 'movie_recommendations')
2025-05-25 21:35:09.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:09.8|Info|FluentMigrator.Runner.MigrationRunner|176: movie_recommendations migrated
2025-05-25 21:35:09.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0373905s
2025-05-25 21:35:09.8|Info|FluentMigrator.Runner.MigrationRunner|177: language_improvements migrating
2025-05-25 21:35:09.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:09.8|Info|language_improvements|Starting migration of Log DB to 177
2025-05-25 21:35:09.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (177, '2025-05-25T19:35:09', 'language_improvements')
2025-05-25 21:35:09.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:09.8|Info|FluentMigrator.Runner.MigrationRunner|177: language_improvements migrated
2025-05-25 21:35:09.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0372055s
2025-05-25 21:35:09.9|Info|FluentMigrator.Runner.MigrationRunner|178: new_list_server migrating
2025-05-25 21:35:09.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:09.9|Info|new_list_server|Starting migration of Log DB to 178
2025-05-25 21:35:09.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (178, '2025-05-25T19:35:09', 'new_list_server')
2025-05-25 21:35:09.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:09.9|Info|FluentMigrator.Runner.MigrationRunner|178: new_list_server migrated
2025-05-25 21:35:09.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0372833s
2025-05-25 21:35:09.9|Info|FluentMigrator.Runner.MigrationRunner|179: movie_translation_indexes migrating
2025-05-25 21:35:09.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:09.9|Info|movie_translation_indexes|Starting migration of Log DB to 179
2025-05-25 21:35:09.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (179, '2025-05-25T19:35:09', 'movie_translation_indexes')
2025-05-25 21:35:10.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:10.0|Info|FluentMigrator.Runner.MigrationRunner|179: movie_translation_indexes migrated
2025-05-25 21:35:10.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0388005s
2025-05-25 21:35:10.0|Info|FluentMigrator.Runner.MigrationRunner|180: fix_invalid_profile_references migrating
2025-05-25 21:35:10.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:10.0|Info|fix_invalid_profile_references|Starting migration of Log DB to 180
2025-05-25 21:35:10.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (180, '2025-05-25T19:35:10', 'fix_invalid_profile_references')
2025-05-25 21:35:10.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:10.0|Info|FluentMigrator.Runner.MigrationRunner|180: fix_invalid_profile_references migrated
2025-05-25 21:35:10.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0368126s
2025-05-25 21:35:10.0|Info|FluentMigrator.Runner.MigrationRunner|181: list_movies_table migrating
2025-05-25 21:35:10.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:10.1|Info|list_movies_table|Starting migration of Log DB to 181
2025-05-25 21:35:10.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (181, '2025-05-25T19:35:10', 'list_movies_table')
2025-05-25 21:35:10.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:10.1|Info|FluentMigrator.Runner.MigrationRunner|181: list_movies_table migrated
2025-05-25 21:35:10.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0375854s
2025-05-25 21:35:10.1|Info|FluentMigrator.Runner.MigrationRunner|182: on_delete_notification migrating
2025-05-25 21:35:10.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:10.1|Info|on_delete_notification|Starting migration of Log DB to 182
2025-05-25 21:35:10.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (182, '2025-05-25T19:35:10', 'on_delete_notification')
2025-05-25 21:35:10.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:10.2|Info|FluentMigrator.Runner.MigrationRunner|182: on_delete_notification migrated
2025-05-25 21:35:10.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0409982s
2025-05-25 21:35:10.2|Info|FluentMigrator.Runner.MigrationRunner|183: download_propers_config migrating
2025-05-25 21:35:10.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:10.2|Info|download_propers_config|Starting migration of Log DB to 183
2025-05-25 21:35:10.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (183, '2025-05-25T19:35:10', 'download_propers_config')
2025-05-25 21:35:10.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:10.2|Info|FluentMigrator.Runner.MigrationRunner|183: download_propers_config migrated
2025-05-25 21:35:10.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0404857s
2025-05-25 21:35:10.2|Info|FluentMigrator.Runner.MigrationRunner|184: add_priority_to_indexers migrating
2025-05-25 21:35:10.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:10.3|Info|add_priority_to_indexers|Starting migration of Log DB to 184
2025-05-25 21:35:10.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (184, '2025-05-25T19:35:10', 'add_priority_to_indexers')
2025-05-25 21:35:10.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:10.3|Info|FluentMigrator.Runner.MigrationRunner|184: add_priority_to_indexers migrated
2025-05-25 21:35:10.3|Info|FluentMigrator.Runner.MigrationRunner|=> 0.038001s
2025-05-25 21:35:10.3|Info|FluentMigrator.Runner.MigrationRunner|185: add_alternative_title_indices migrating
2025-05-25 21:35:10.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:10.3|Info|add_alternative_title_indices|Starting migration of Log DB to 185
2025-05-25 21:35:10.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (185, '2025-05-25T19:35:10', 'add_alternative_title_indices')
2025-05-25 21:35:10.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:10.4|Info|FluentMigrator.Runner.MigrationRunner|185: add_alternative_title_indices migrated
2025-05-25 21:35:10.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0369698s
2025-05-25 21:35:10.4|Info|FluentMigrator.Runner.MigrationRunner|186: fix_tmdb_duplicates migrating
2025-05-25 21:35:10.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:10.4|Info|fix_tmdb_duplicates|Starting migration of Log DB to 186
2025-05-25 21:35:10.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (186, '2025-05-25T19:35:10', 'fix_tmdb_duplicates')
2025-05-25 21:35:10.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:10.4|Info|FluentMigrator.Runner.MigrationRunner|186: fix_tmdb_duplicates migrated
2025-05-25 21:35:10.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0366478s
2025-05-25 21:35:10.4|Info|FluentMigrator.Runner.MigrationRunner|187: swap_filechmod_for_folderchmod migrating
2025-05-25 21:35:10.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:10.4|Info|swap_filechmod_for_folderchmod|Starting migration of Log DB to 187
2025-05-25 21:35:10.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (187, '2025-05-25T19:35:10', 'swap_filechmod_for_folderchmod')
2025-05-25 21:35:10.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:10.5|Info|FluentMigrator.Runner.MigrationRunner|187: swap_filechmod_for_folderchmod migrated
2025-05-25 21:35:10.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0381463s
2025-05-25 21:35:10.5|Info|FluentMigrator.Runner.MigrationRunner|188: mediainfo_channels migrating
2025-05-25 21:35:10.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:10.5|Info|mediainfo_channels|Starting migration of Log DB to 188
2025-05-25 21:35:10.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (188, '2025-05-25T19:35:10', 'mediainfo_channels')
2025-05-25 21:35:10.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:10.5|Info|FluentMigrator.Runner.MigrationRunner|188: mediainfo_channels migrated
2025-05-25 21:35:10.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0374982s
2025-05-25 21:35:10.6|Info|FluentMigrator.Runner.MigrationRunner|189: add_update_history migrating
2025-05-25 21:35:10.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:10.6|Info|add_update_history|Starting migration of Log DB to 189
2025-05-25 21:35:10.6|Info|FluentMigrator.Runner.MigrationRunner|CreateTable UpdateHistory
2025-05-25 21:35:10.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|CREATE TABLE "UpdateHistory" ("Id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT, "Date" DATETIME NOT NULL, "Version" TEXT NOT NULL, "EventType" INTEGER NOT NULL)
2025-05-25 21:35:10.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0086841s
2025-05-25 21:35:10.6|Info|FluentMigrator.Runner.MigrationRunner|CreateIndex UpdateHistory (Date)
2025-05-25 21:35:10.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|CREATE INDEX "IX_UpdateHistory_Date" ON "UpdateHistory" ("Date" ASC)
2025-05-25 21:35:10.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0086031s
2025-05-25 21:35:10.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (189, '2025-05-25T19:35:10', 'add_update_history')
2025-05-25 21:35:10.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:10.7|Info|FluentMigrator.Runner.MigrationRunner|189: add_update_history migrated
2025-05-25 21:35:10.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0372273s
2025-05-25 21:35:10.7|Info|FluentMigrator.Runner.MigrationRunner|190: update_awesome_hd_link migrating
2025-05-25 21:35:10.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:10.7|Info|update_awesome_hd_link|Starting migration of Log DB to 190
2025-05-25 21:35:10.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (190, '2025-05-25T19:35:10', 'update_awesome_hd_link')
2025-05-25 21:35:10.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:10.7|Info|FluentMigrator.Runner.MigrationRunner|190: update_awesome_hd_link migrated
2025-05-25 21:35:10.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0401885s
2025-05-25 21:35:10.7|Info|FluentMigrator.Runner.MigrationRunner|191: remove_awesomehd migrating
2025-05-25 21:35:10.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:10.8|Info|remove_awesomehd|Starting migration of Log DB to 191
2025-05-25 21:35:10.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (191, '2025-05-25T19:35:10', 'remove_awesomehd')
2025-05-25 21:35:10.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:10.8|Info|FluentMigrator.Runner.MigrationRunner|191: remove_awesomehd migrated
2025-05-25 21:35:10.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0369819s
2025-05-25 21:35:10.8|Info|FluentMigrator.Runner.MigrationRunner|192: add_on_delete_to_notifications migrating
2025-05-25 21:35:10.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:10.8|Info|add_on_delete_to_notifications|Starting migration of Log DB to 192
2025-05-25 21:35:10.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (192, '2025-05-25T19:35:10', 'add_on_delete_to_notifications')
2025-05-25 21:35:10.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:10.9|Info|FluentMigrator.Runner.MigrationRunner|192: add_on_delete_to_notifications migrated
2025-05-25 21:35:10.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0367415s
2025-05-25 21:35:10.9|Info|FluentMigrator.Runner.MigrationRunner|194: add_bypass_to_delay_profile migrating
2025-05-25 21:35:10.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:10.9|Info|add_bypass_to_delay_profile|Starting migration of Log DB to 194
2025-05-25 21:35:10.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (194, '2025-05-25T19:35:10', 'add_bypass_to_delay_profile')
2025-05-25 21:35:10.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:10.9|Info|FluentMigrator.Runner.MigrationRunner|194: add_bypass_to_delay_profile migrated
2025-05-25 21:35:10.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0363218s
2025-05-25 21:35:10.9|Info|FluentMigrator.Runner.MigrationRunner|195: update_notifiarr migrating
2025-05-25 21:35:10.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.0|Info|update_notifiarr|Starting migration of Log DB to 195
2025-05-25 21:35:11.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (195, '2025-05-25T19:35:11', 'update_notifiarr')
2025-05-25 21:35:11.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.0|Info|FluentMigrator.Runner.MigrationRunner|195: update_notifiarr migrated
2025-05-25 21:35:11.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0385131s
2025-05-25 21:35:11.0|Info|FluentMigrator.Runner.MigrationRunner|196: legacy_mediainfo_hdr migrating
2025-05-25 21:35:11.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.0|Info|legacy_mediainfo_hdr|Starting migration of Log DB to 196
2025-05-25 21:35:11.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (196, '2025-05-25T19:35:11', 'legacy_mediainfo_hdr')
2025-05-25 21:35:11.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.0|Info|FluentMigrator.Runner.MigrationRunner|196: legacy_mediainfo_hdr migrated
2025-05-25 21:35:11.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0363578s
2025-05-25 21:35:11.1|Info|FluentMigrator.Runner.MigrationRunner|197: rename_blacklist_to_blocklist migrating
2025-05-25 21:35:11.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.1|Info|rename_blacklist_to_blocklist|Starting migration of Log DB to 197
2025-05-25 21:35:11.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (197, '2025-05-25T19:35:11', 'rename_blacklist_to_blocklist')
2025-05-25 21:35:11.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.1|Info|FluentMigrator.Runner.MigrationRunner|197: rename_blacklist_to_blocklist migrated
2025-05-25 21:35:11.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.043062s
2025-05-25 21:35:11.1|Info|FluentMigrator.Runner.MigrationRunner|198: add_indexer_tags migrating
2025-05-25 21:35:11.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.1|Info|add_indexer_tags|Starting migration of Log DB to 198
2025-05-25 21:35:11.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (198, '2025-05-25T19:35:11', 'add_indexer_tags')
2025-05-25 21:35:11.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.2|Info|FluentMigrator.Runner.MigrationRunner|198: add_indexer_tags migrated
2025-05-25 21:35:11.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.033338s
2025-05-25 21:35:11.2|Info|FluentMigrator.Runner.MigrationRunner|199: mediainfo_to_ffmpeg migrating
2025-05-25 21:35:11.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.2|Info|mediainfo_to_ffmpeg|Starting migration of Log DB to 199
2025-05-25 21:35:11.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (199, '2025-05-25T19:35:11', 'mediainfo_to_ffmpeg')
2025-05-25 21:35:11.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.2|Info|FluentMigrator.Runner.MigrationRunner|199: mediainfo_to_ffmpeg migrated
2025-05-25 21:35:11.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0353185s
2025-05-25 21:35:11.2|Info|FluentMigrator.Runner.MigrationRunner|200: cdh_per_downloadclient migrating
2025-05-25 21:35:11.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.3|Info|cdh_per_downloadclient|Starting migration of Log DB to 200
2025-05-25 21:35:11.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (200, '2025-05-25T19:35:11', 'cdh_per_downloadclient')
2025-05-25 21:35:11.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.3|Info|FluentMigrator.Runner.MigrationRunner|200: cdh_per_downloadclient migrated
2025-05-25 21:35:11.3|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0345912s
2025-05-25 21:35:11.3|Info|FluentMigrator.Runner.MigrationRunner|201: migrate_discord_from_slack migrating
2025-05-25 21:35:11.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.3|Info|migrate_discord_from_slack|Starting migration of Log DB to 201
2025-05-25 21:35:11.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (201, '2025-05-25T19:35:11', 'migrate_discord_from_slack')
2025-05-25 21:35:11.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.3|Info|FluentMigrator.Runner.MigrationRunner|201: migrate_discord_from_slack migrated
2025-05-25 21:35:11.3|Info|FluentMigrator.Runner.MigrationRunner|=> 0.032415s
2025-05-25 21:35:11.4|Info|FluentMigrator.Runner.MigrationRunner|202: remove_predb migrating
2025-05-25 21:35:11.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.4|Info|remove_predb|Starting migration of Log DB to 202
2025-05-25 21:35:11.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (202, '2025-05-25T19:35:11', 'remove_predb')
2025-05-25 21:35:11.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.4|Info|FluentMigrator.Runner.MigrationRunner|202: remove_predb migrated
2025-05-25 21:35:11.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0333944s
2025-05-25 21:35:11.4|Info|FluentMigrator.Runner.MigrationRunner|203: add_on_update_to_notifications migrating
2025-05-25 21:35:11.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.4|Info|add_on_update_to_notifications|Starting migration of Log DB to 203
2025-05-25 21:35:11.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (203, '2025-05-25T19:35:11', 'add_on_update_to_notifications')
2025-05-25 21:35:11.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.5|Info|FluentMigrator.Runner.MigrationRunner|203: add_on_update_to_notifications migrated
2025-05-25 21:35:11.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0339917s
2025-05-25 21:35:11.5|Info|FluentMigrator.Runner.MigrationRunner|204: ensure_identity_on_id_columns migrating
2025-05-25 21:35:11.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.5|Info|ensure_identity_on_id_columns|Starting migration of Log DB to 204
2025-05-25 21:35:11.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (204, '2025-05-25T19:35:11', 'ensure_identity_on_id_columns')
2025-05-25 21:35:11.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.5|Info|FluentMigrator.Runner.MigrationRunner|204: ensure_identity_on_id_columns migrated
2025-05-25 21:35:11.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0303163s
2025-05-25 21:35:11.5|Info|FluentMigrator.Runner.MigrationRunner|205: download_client_per_indexer migrating
2025-05-25 21:35:11.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.5|Info|download_client_per_indexer|Starting migration of Log DB to 205
2025-05-25 21:35:11.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (205, '2025-05-25T19:35:11', 'download_client_per_indexer')
2025-05-25 21:35:11.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.6|Info|FluentMigrator.Runner.MigrationRunner|205: download_client_per_indexer migrated
2025-05-25 21:35:11.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0360394s
2025-05-25 21:35:11.6|Info|FluentMigrator.Runner.MigrationRunner|206: multiple_ratings_support migrating
2025-05-25 21:35:11.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.6|Info|multiple_ratings_support|Starting migration of Log DB to 206
2025-05-25 21:35:11.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (206, '2025-05-25T19:35:11', 'multiple_ratings_support')
2025-05-25 21:35:11.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.6|Info|FluentMigrator.Runner.MigrationRunner|206: multiple_ratings_support migrated
2025-05-25 21:35:11.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0370062s
2025-05-25 21:35:11.6|Info|FluentMigrator.Runner.MigrationRunner|207: movie_metadata migrating
2025-05-25 21:35:11.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.7|Info|movie_metadata|Starting migration of Log DB to 207
2025-05-25 21:35:11.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (207, '2025-05-25T19:35:11', 'movie_metadata')
2025-05-25 21:35:11.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.7|Info|FluentMigrator.Runner.MigrationRunner|207: movie_metadata migrated
2025-05-25 21:35:11.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0358039s
2025-05-25 21:35:11.7|Info|FluentMigrator.Runner.MigrationRunner|208: collections migrating
2025-05-25 21:35:11.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.7|Info|collections|Starting migration of Log DB to 208
2025-05-25 21:35:11.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (208, '2025-05-25T19:35:11', 'collections')
2025-05-25 21:35:11.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.7|Info|FluentMigrator.Runner.MigrationRunner|208: collections migrated
2025-05-25 21:35:11.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0366578s
2025-05-25 21:35:11.8|Info|FluentMigrator.Runner.MigrationRunner|209: movie_meta_collection_index migrating
2025-05-25 21:35:11.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.8|Info|movie_meta_collection_index|Starting migration of Log DB to 209
2025-05-25 21:35:11.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (209, '2025-05-25T19:35:11', 'movie_meta_collection_index')
2025-05-25 21:35:11.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.8|Info|FluentMigrator.Runner.MigrationRunner|209: movie_meta_collection_index migrated
2025-05-25 21:35:11.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0364484s
2025-05-25 21:35:11.8|Info|FluentMigrator.Runner.MigrationRunner|210: movie_added_notifications migrating
2025-05-25 21:35:11.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.8|Info|movie_added_notifications|Starting migration of Log DB to 210
2025-05-25 21:35:11.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (210, '2025-05-25T19:35:11', 'movie_added_notifications')
2025-05-25 21:35:11.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.9|Info|FluentMigrator.Runner.MigrationRunner|210: movie_added_notifications migrated
2025-05-25 21:35:11.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0360721s
2025-05-25 21:35:11.9|Info|FluentMigrator.Runner.MigrationRunner|211: more_movie_meta_index migrating
2025-05-25 21:35:11.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:11.9|Info|more_movie_meta_index|Starting migration of Log DB to 211
2025-05-25 21:35:11.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (211, '2025-05-25T19:35:11', 'more_movie_meta_index')
2025-05-25 21:35:11.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:11.9|Info|FluentMigrator.Runner.MigrationRunner|211: more_movie_meta_index migrated
2025-05-25 21:35:11.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0371695s
2025-05-25 21:35:12.0|Info|FluentMigrator.Runner.MigrationRunner|212: postgres_update_timestamp_columns_to_with_timezone migrating
2025-05-25 21:35:12.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:12.0|Info|postgres_update_timestamp_columns_to_with_timezone|Starting migration of Log DB to 212
2025-05-25 21:35:12.0|Info|FluentMigrator.Runner.MigrationRunner|AlterTable Logs
2025-05-25 21:35:12.0|Info|FluentMigrator.Runner.MigrationRunner|=> 3.1E-06s
2025-05-25 21:35:12.0|Info|FluentMigrator.Runner.MigrationRunner|AlterColumn Logs Time DateTimeOffset
2025-05-25 21:35:12.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|CREATE TABLE "Logs_temp" ("Id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT, "Message" TEXT NOT NULL, "Time" DATETIME NOT NULL, "Logger" TEXT NOT NULL, "Exception" TEXT, "ExceptionType" TEXT, "Level" TEXT NOT NULL)
2025-05-25 21:35:12.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "Logs_temp" ("Id", "Message", "Time", "Logger", "Exception", "ExceptionType", "Level") SELECT "Id", "Message", "Time", "Logger", "Exception", "ExceptionType", "Level" FROM "Logs"
2025-05-25 21:35:12.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|DROP TABLE "Logs"
2025-05-25 21:35:12.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|ALTER TABLE "Logs_temp" RENAME TO "Logs"
2025-05-25 21:35:12.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|CREATE INDEX "IX_Logs_Time" ON "Logs" ("Time" ASC)
2025-05-25 21:35:12.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0373992s
2025-05-25 21:35:12.0|Info|FluentMigrator.Runner.MigrationRunner|AlterTable UpdateHistory
2025-05-25 21:35:12.1|Info|FluentMigrator.Runner.MigrationRunner|=> 3.1E-06s
2025-05-25 21:35:12.1|Info|FluentMigrator.Runner.MigrationRunner|AlterColumn UpdateHistory Date DateTimeOffset
2025-05-25 21:35:12.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|CREATE TABLE "UpdateHistory_temp" ("Id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT, "Date" DATETIME NOT NULL, "Version" TEXT NOT NULL, "EventType" INTEGER NOT NULL)
2025-05-25 21:35:12.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "UpdateHistory_temp" ("Id", "Date", "Version", "EventType") SELECT "Id", "Date", "Version", "EventType" FROM "UpdateHistory"
2025-05-25 21:35:12.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|DROP TABLE "UpdateHistory"
2025-05-25 21:35:12.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|ALTER TABLE "UpdateHistory_temp" RENAME TO "UpdateHistory"
2025-05-25 21:35:12.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|CREATE INDEX "IX_UpdateHistory_Date" ON "UpdateHistory" ("Date" ASC)
2025-05-25 21:35:12.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0371009s
2025-05-25 21:35:12.1|Info|FluentMigrator.Runner.MigrationRunner|AlterTable VersionInfo
2025-05-25 21:35:12.1|Info|FluentMigrator.Runner.MigrationRunner|=> 2.9E-06s
2025-05-25 21:35:12.1|Info|FluentMigrator.Runner.MigrationRunner|AlterColumn VersionInfo AppliedOn DateTimeOffset
2025-05-25 21:35:12.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|CREATE TABLE "VersionInfo_temp" ("Version" INTEGER NOT NULL, "AppliedOn" DATETIME, "Description" TEXT)
2025-05-25 21:35:12.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo_temp" ("Version", "AppliedOn", "Description") SELECT "Version", "AppliedOn", "Description" FROM "VersionInfo"
2025-05-25 21:35:12.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|DROP TABLE "VersionInfo"
2025-05-25 21:35:12.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|ALTER TABLE "VersionInfo_temp" RENAME TO "VersionInfo"
2025-05-25 21:35:12.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|CREATE UNIQUE INDEX "UC_Version" ON "VersionInfo" ("Version" ASC)
2025-05-25 21:35:12.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0412159s
2025-05-25 21:35:12.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (212, '2025-05-25T19:35:12', 'postgres_update_timestamp_columns_to_with_timezone')
2025-05-25 21:35:12.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:12.2|Info|FluentMigrator.Runner.MigrationRunner|212: postgres_update_timestamp_columns_to_with_timezone migrated
2025-05-25 21:35:12.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0691513s
2025-05-25 21:35:12.2|Info|FluentMigrator.Runner.MigrationRunner|214: add_language_tags_to_subtitle_files migrating
2025-05-25 21:35:12.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:12.2|Info|add_language_tags_to_subtitle_files|Starting migration of Log DB to 214
2025-05-25 21:35:12.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (214, '2025-05-25T19:35:12', 'add_language_tags_to_subtitle_files')
2025-05-25 21:35:12.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:12.3|Info|FluentMigrator.Runner.MigrationRunner|214: add_language_tags_to_subtitle_files migrated
2025-05-25 21:35:12.3|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0334607s
2025-05-25 21:35:12.3|Info|FluentMigrator.Runner.MigrationRunner|215: add_salt_to_users migrating
2025-05-25 21:35:12.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:12.3|Info|add_salt_to_users|Starting migration of Log DB to 215
2025-05-25 21:35:12.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (215, '2025-05-25T19:35:12', 'add_salt_to_users')
2025-05-25 21:35:12.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:12.3|Info|FluentMigrator.Runner.MigrationRunner|215: add_salt_to_users migrated
2025-05-25 21:35:12.3|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0316777s
2025-05-25 21:35:12.3|Info|FluentMigrator.Runner.MigrationRunner|216: clean_alt_titles migrating
2025-05-25 21:35:12.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:12.3|Info|clean_alt_titles|Starting migration of Log DB to 216
2025-05-25 21:35:12.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (216, '2025-05-25T19:35:12', 'clean_alt_titles')
2025-05-25 21:35:12.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:12.4|Info|FluentMigrator.Runner.MigrationRunner|216: clean_alt_titles migrated
2025-05-25 21:35:12.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0309914s
2025-05-25 21:35:12.4|Info|FluentMigrator.Runner.MigrationRunner|217: remove_omg migrating
2025-05-25 21:35:12.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:12.4|Info|remove_omg|Starting migration of Log DB to 217
2025-05-25 21:35:12.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (217, '2025-05-25T19:35:12', 'remove_omg')
2025-05-25 21:35:12.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:12.4|Info|FluentMigrator.Runner.MigrationRunner|217: remove_omg migrated
2025-05-25 21:35:12.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0297197s
2025-05-25 21:35:12.4|Info|FluentMigrator.Runner.MigrationRunner|218: add_additional_info_to_pending_releases migrating
2025-05-25 21:35:12.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:12.4|Info|add_additional_info_to_pending_releases|Starting migration of Log DB to 218
2025-05-25 21:35:12.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (218, '2025-05-25T19:35:12', 'add_additional_info_to_pending_releases')
2025-05-25 21:35:12.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:12.5|Info|FluentMigrator.Runner.MigrationRunner|218: add_additional_info_to_pending_releases migrated
2025-05-25 21:35:12.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0321169s
2025-05-25 21:35:12.5|Info|FluentMigrator.Runner.MigrationRunner|219: add_result_to_commands migrating
2025-05-25 21:35:12.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:12.5|Info|add_result_to_commands|Starting migration of Log DB to 219
2025-05-25 21:35:12.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (219, '2025-05-25T19:35:12', 'add_result_to_commands')
2025-05-25 21:35:12.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:12.5|Info|FluentMigrator.Runner.MigrationRunner|219: add_result_to_commands migrated
2025-05-25 21:35:12.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0296999s
2025-05-25 21:35:12.5|Info|FluentMigrator.Runner.MigrationRunner|220: health_restored_notification migrating
2025-05-25 21:35:12.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:12.5|Info|health_restored_notification|Starting migration of Log DB to 220
2025-05-25 21:35:12.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (220, '2025-05-25T19:35:12', 'health_restored_notification')
2025-05-25 21:35:12.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:12.6|Info|FluentMigrator.Runner.MigrationRunner|220: health_restored_notification migrated
2025-05-25 21:35:12.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.030953s
2025-05-25 21:35:12.6|Info|FluentMigrator.Runner.MigrationRunner|221: add_on_manual_interaction_required_to_notifications migrating
2025-05-25 21:35:12.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:12.6|Info|add_on_manual_interaction_required_to_notifications|Starting migration of Log DB to 221
2025-05-25 21:35:12.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (221, '2025-05-25T19:35:12', 'add_on_manual_interaction_required_to_notifications')
2025-05-25 21:35:12.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:12.6|Info|FluentMigrator.Runner.MigrationRunner|221: add_on_manual_interaction_required_to_notifications migrated
2025-05-25 21:35:12.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0316071s
2025-05-25 21:35:12.6|Info|FluentMigrator.Runner.MigrationRunner|222: remove_rarbg migrating
2025-05-25 21:35:12.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:12.7|Info|remove_rarbg|Starting migration of Log DB to 222
2025-05-25 21:35:12.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (222, '2025-05-25T19:35:12', 'remove_rarbg')
2025-05-25 21:35:12.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:12.7|Info|FluentMigrator.Runner.MigrationRunner|222: remove_rarbg migrated
2025-05-25 21:35:12.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.02978s
2025-05-25 21:35:12.7|Info|FluentMigrator.Runner.MigrationRunner|223: remove_invalid_roksbox_metadata_images migrating
2025-05-25 21:35:12.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:12.7|Info|remove_invalid_roksbox_metadata_images|Starting migration of Log DB to 223
2025-05-25 21:35:12.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (223, '2025-05-25T19:35:12', 'remove_invalid_roksbox_metadata_images')
2025-05-25 21:35:12.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:12.7|Info|FluentMigrator.Runner.MigrationRunner|223: remove_invalid_roksbox_metadata_images migrated
2025-05-25 21:35:12.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0315033s
2025-05-25 21:35:12.7|Info|FluentMigrator.Runner.MigrationRunner|224: list_sync_time migrating
2025-05-25 21:35:12.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:12.8|Info|list_sync_time|Starting migration of Log DB to 224
2025-05-25 21:35:12.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (224, '2025-05-25T19:35:12', 'list_sync_time')
2025-05-25 21:35:12.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:12.8|Info|FluentMigrator.Runner.MigrationRunner|224: list_sync_time migrated
2025-05-25 21:35:12.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0294087s
2025-05-25 21:35:12.8|Info|FluentMigrator.Runner.MigrationRunner|225: add_tags_to_collections migrating
2025-05-25 21:35:12.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:12.8|Info|add_tags_to_collections|Starting migration of Log DB to 225
2025-05-25 21:35:12.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (225, '2025-05-25T19:35:12', 'add_tags_to_collections')
2025-05-25 21:35:12.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:12.8|Info|FluentMigrator.Runner.MigrationRunner|225: add_tags_to_collections migrated
2025-05-25 21:35:12.8|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0297537s
2025-05-25 21:35:12.8|Info|FluentMigrator.Runner.MigrationRunner|226: add_download_client_tags migrating
2025-05-25 21:35:12.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:12.9|Info|add_download_client_tags|Starting migration of Log DB to 226
2025-05-25 21:35:12.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (226, '2025-05-25T19:35:12', 'add_download_client_tags')
2025-05-25 21:35:12.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:12.9|Info|FluentMigrator.Runner.MigrationRunner|226: add_download_client_tags migrated
2025-05-25 21:35:12.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0298051s
2025-05-25 21:35:12.9|Info|FluentMigrator.Runner.MigrationRunner|227: add_auto_tagging migrating
2025-05-25 21:35:12.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:12.9|Info|add_auto_tagging|Starting migration of Log DB to 227
2025-05-25 21:35:12.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (227, '2025-05-25T19:35:12', 'add_auto_tagging')
2025-05-25 21:35:12.9|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:12.9|Info|FluentMigrator.Runner.MigrationRunner|227: add_auto_tagging migrated
2025-05-25 21:35:12.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0320007s
2025-05-25 21:35:12.9|Info|FluentMigrator.Runner.MigrationRunner|228: add_custom_format_score_bypass_to_delay_profile migrating
2025-05-25 21:35:13.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:13.0|Info|add_custom_format_score_bypass_to_delay_profile|Starting migration of Log DB to 228
2025-05-25 21:35:13.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (228, '2025-05-25T19:35:13', 'add_custom_format_score_bypass_to_delay_profile')
2025-05-25 21:35:13.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:13.0|Info|FluentMigrator.Runner.MigrationRunner|228: add_custom_format_score_bypass_to_delay_profile migrated
2025-05-25 21:35:13.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0315807s
2025-05-25 21:35:13.0|Info|FluentMigrator.Runner.MigrationRunner|229: update_restrictions_to_release_profiles migrating
2025-05-25 21:35:13.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:13.0|Info|update_restrictions_to_release_profiles|Starting migration of Log DB to 229
2025-05-25 21:35:13.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (229, '2025-05-25T19:35:13', 'update_restrictions_to_release_profiles')
2025-05-25 21:35:13.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:13.0|Info|FluentMigrator.Runner.MigrationRunner|229: update_restrictions_to_release_profiles migrated
2025-05-25 21:35:13.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0301247s
2025-05-25 21:35:13.0|Info|FluentMigrator.Runner.MigrationRunner|230: rename_quality_profiles migrating
2025-05-25 21:35:13.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:13.1|Info|rename_quality_profiles|Starting migration of Log DB to 230
2025-05-25 21:35:13.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (230, '2025-05-25T19:35:13', 'rename_quality_profiles')
2025-05-25 21:35:13.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:13.1|Info|FluentMigrator.Runner.MigrationRunner|230: rename_quality_profiles migrated
2025-05-25 21:35:13.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0296196s
2025-05-25 21:35:13.1|Info|FluentMigrator.Runner.MigrationRunner|231: update_images_remote_url migrating
2025-05-25 21:35:13.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:13.1|Info|update_images_remote_url|Starting migration of Log DB to 231
2025-05-25 21:35:13.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (231, '2025-05-25T19:35:13', 'update_images_remote_url')
2025-05-25 21:35:13.1|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:13.1|Info|FluentMigrator.Runner.MigrationRunner|231: update_images_remote_url migrated
2025-05-25 21:35:13.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0307959s
2025-05-25 21:35:13.2|Info|FluentMigrator.Runner.MigrationRunner|232: add_notification_status migrating
2025-05-25 21:35:13.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:13.2|Info|add_notification_status|Starting migration of Log DB to 232
2025-05-25 21:35:13.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (232, '2025-05-25T19:35:13', 'add_notification_status')
2025-05-25 21:35:13.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:13.2|Info|FluentMigrator.Runner.MigrationRunner|232: add_notification_status migrated
2025-05-25 21:35:13.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0308121s
2025-05-25 21:35:13.2|Info|FluentMigrator.Runner.MigrationRunner|233: rename_deprecated_indexer_flags migrating
2025-05-25 21:35:13.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:13.2|Info|rename_deprecated_indexer_flags|Starting migration of Log DB to 233
2025-05-25 21:35:13.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (233, '2025-05-25T19:35:13', 'rename_deprecated_indexer_flags')
2025-05-25 21:35:13.2|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:13.2|Info|FluentMigrator.Runner.MigrationRunner|233: rename_deprecated_indexer_flags migrated
2025-05-25 21:35:13.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0312376s
2025-05-25 21:35:13.2|Info|FluentMigrator.Runner.MigrationRunner|234: movie_last_searched_time migrating
2025-05-25 21:35:13.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:13.3|Info|movie_last_searched_time|Starting migration of Log DB to 234
2025-05-25 21:35:13.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (234, '2025-05-25T19:35:13', 'movie_last_searched_time')
2025-05-25 21:35:13.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:13.3|Info|FluentMigrator.Runner.MigrationRunner|234: movie_last_searched_time migrated
2025-05-25 21:35:13.3|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0319938s
2025-05-25 21:35:13.3|Info|FluentMigrator.Runner.MigrationRunner|235: email_encryption migrating
2025-05-25 21:35:13.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:13.3|Info|email_encryption|Starting migration of Log DB to 235
2025-05-25 21:35:13.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (235, '2025-05-25T19:35:13', 'email_encryption')
2025-05-25 21:35:13.3|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:13.3|Info|FluentMigrator.Runner.MigrationRunner|235: email_encryption migrated
2025-05-25 21:35:13.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0295006s
2025-05-25 21:35:13.4|Info|FluentMigrator.Runner.MigrationRunner|236: clear_last_rss_releases_info migrating
2025-05-25 21:35:13.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:13.4|Info|clear_last_rss_releases_info|Starting migration of Log DB to 236
2025-05-25 21:35:13.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (236, '2025-05-25T19:35:13', 'clear_last_rss_releases_info')
2025-05-25 21:35:13.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:13.4|Info|FluentMigrator.Runner.MigrationRunner|236: clear_last_rss_releases_info migrated
2025-05-25 21:35:13.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0299986s
2025-05-25 21:35:13.4|Info|FluentMigrator.Runner.MigrationRunner|237: add_indexes migrating
2025-05-25 21:35:13.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:13.4|Info|add_indexes|Starting migration of Log DB to 237
2025-05-25 21:35:13.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (237, '2025-05-25T19:35:13', 'add_indexes')
2025-05-25 21:35:13.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:13.5|Info|FluentMigrator.Runner.MigrationRunner|237: add_indexes migrated
2025-05-25 21:35:13.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.034894s
2025-05-25 21:35:13.5|Info|FluentMigrator.Runner.MigrationRunner|238: parse_title_from_existing_subtitle_files migrating
2025-05-25 21:35:13.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:13.5|Info|parse_title_from_existing_subtitle_files|Starting migration of Log DB to 238
2025-05-25 21:35:13.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (238, '2025-05-25T19:35:13', 'parse_title_from_existing_subtitle_files')
2025-05-25 21:35:13.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:13.5|Info|FluentMigrator.Runner.MigrationRunner|238: parse_title_from_existing_subtitle_files migrated
2025-05-25 21:35:13.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0334993s
2025-05-25 21:35:13.5|Info|FluentMigrator.Runner.MigrationRunner|239: add_minimum_upgrade_format_score_to_quality_profiles migrating
2025-05-25 21:35:13.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:13.5|Info|add_minimum_upgrade_format_score_to_quality_profiles|Starting migration of Log DB to 239
2025-05-25 21:35:13.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (239, '2025-05-25T19:35:13', 'add_minimum_upgrade_format_score_to_quality_profiles')
2025-05-25 21:35:13.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:13.6|Info|FluentMigrator.Runner.MigrationRunner|239: add_minimum_upgrade_format_score_to_quality_profiles migrated
2025-05-25 21:35:13.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0316057s
2025-05-25 21:35:13.6|Info|FluentMigrator.Runner.MigrationRunner|240: drop_multi_episode_style_from_naming_config migrating
2025-05-25 21:35:13.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:13.6|Info|drop_multi_episode_style_from_naming_config|Starting migration of Log DB to 240
2025-05-25 21:35:13.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (240, '2025-05-25T19:35:13', 'drop_multi_episode_style_from_naming_config')
2025-05-25 21:35:13.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:13.6|Info|FluentMigrator.Runner.MigrationRunner|240: drop_multi_episode_style_from_naming_config migrated
2025-05-25 21:35:13.6|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0327645s
2025-05-25 21:35:13.6|Info|FluentMigrator.Runner.MigrationRunner|241: stevenlu_update_url migrating
2025-05-25 21:35:13.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Beginning Transaction
2025-05-25 21:35:13.6|Info|stevenlu_update_url|Starting migration of Log DB to 241
2025-05-25 21:35:13.6|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|INSERT INTO "VersionInfo" ("Version", "AppliedOn", "Description") VALUES (241, '2025-05-25T19:35:13', 'stevenlu_update_url')
2025-05-25 21:35:13.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Committing Transaction
2025-05-25 21:35:13.7|Info|FluentMigrator.Runner.MigrationRunner|241: stevenlu_update_url migrated
2025-05-25 21:35:13.7|Info|FluentMigrator.Runner.MigrationRunner|=> 0.03442s
2025-05-25 21:35:13.7|Debug|MigrationController|Took: 00:00:09.1761383
2025-05-25 21:35:13.8|Info|Microsoft.Hosting.Lifetime|Now listening on: http://[::]:7878
2025-05-25 21:35:13.9|Info|UpdaterConfigProvider|Update mechanism BuiltIn not supported in the current configuration, changing to Docker.
2025-05-25 21:35:14.0|Debug|QualityDefinitionService|Setting up default quality config
2025-05-25 21:35:14.1|Info|QualityProfileService|Setting up default quality profiles
2025-05-25 21:35:14.1|Debug|NotificationFactory|Initializing Providers. Count 26
2025-05-25 21:35:14.1|Info|CommandExecutor|Starting 2 threads for tasks.
2025-05-25 21:35:14.2|Debug|IndexerFactory|Initializing Providers. Count 9
2025-05-25 21:35:14.2|Debug|ImportListFactory|Initializing Providers. Count 19
2025-05-25 21:35:14.2|Debug|MetadataFactory|Initializing Providers. Count 5
2025-05-25 21:35:14.2|Debug|DownloadClientFactory|Initializing Providers. Count 18
2025-05-25 21:35:14.2|Info|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.
2025-05-25 21:35:14.2|Info|Microsoft.Hosting.Lifetime|Hosting environment: Production
2025-05-25 21:35:14.2|Info|Microsoft.Hosting.Lifetime|Content root path: /app/radarr/bin
2025-05-25 21:35:14.4|Debug|Radarr.Http.Authentication.ApiKeyAuthenticationHandler|AuthenticationScheme: API was not authenticated.
2025-05-25 21:35:16.3|Info|ManagedHttpDispatcher|IPv4 is available: True, IPv6 will be disabled
2025-05-25 21:40:14.2|Debug|ImportListSyncService|No enabled import lists, skipping sync and cleaning
2025-05-25 21:41:01.9|Info|Microsoft.Hosting.Lifetime|Application is shutting down...
2025-05-25 21:41:02.1|Info|ConsoleApp|Exiting main.
