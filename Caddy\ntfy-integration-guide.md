# Guía de Integración Ntfy con Media Server Stack

## 🔧 Configuración de Ntfy

### URLs de tu sistema:
- **Ntfy Web Interface**: https://tankenotify.duckdns.org
- **API Base URL**: https://tankenotify.duckdns.org

### Tópicos recomendados:
- `media-downloads` - Descargas completadas (Sonarr/Radarr)
- `media-requests` - Nuevas solicitudes (Jellyseerr)
- `system-alerts` - Alertas del sistema
- `torrent-status` - Estado de torrents (qBittorrent)
- `health-checks` - Monitoreo de salud

## 📱 Configuración de Apps

### Android:
1. Instalar app "ntfy" desde Google Play Store
2. Agregar servidor: `https://tankenotify.duckdns.org`
3. Suscribirse a tópicos: `media-downloads`, `media-requests`, etc.

### Windows:
1. Usar interfaz web: `https://tankenotify.duckdns.org`
2. O usar cliente de escritorio (disponible en GitHub)

## 🔗 Configuración por Aplicación

### SONARR - Notificaciones de Series
**Settings → Connect → Add Notification → Webhook**

```
Name: Ntfy - Series Downloaded
URL: https://tankenotify.duckdns.org/media-downloads
Method: POST
Username: (tu usuario ntfy)
Password: (tu password ntfy)

Body:
{
  "topic": "media-downloads",
  "title": "📺 Serie Descargada",
  "message": "{{Series.Title}} - {{Episode.Title}} (S{{Episode.SeasonNumber}}E{{Episode.EpisodeNumber}})",
  "priority": 3,
  "tags": ["tv", "download"],
  "click": "https://tankeflix.duckdns.org"
}

Triggers:
✅ On Download
✅ On Upgrade
```

### RADARR - Notificaciones de Películas
**Settings → Connect → Add Notification → Webhook**

```
Name: Ntfy - Movies Downloaded
URL: https://tankenotify.duckdns.org/media-downloads
Method: POST
Username: (tu usuario ntfy)
Password: (tu password ntfy)

Body:
{
  "topic": "media-downloads",
  "title": "🎬 Película Descargada",
  "message": "{{Movie.Title}} ({{Movie.Year}}) - {{Quality.Quality.Name}}",
  "priority": 3,
  "tags": ["movie", "download"],
  "click": "https://tankeflix.duckdns.org"
}

Triggers:
✅ On Download
✅ On Upgrade
```

### PROWLARR - Notificaciones de Indexadores
**Settings → Connect → Add Notification → Webhook**

```
Name: Ntfy - Indexer Health
URL: https://tankenotify.duckdns.org/system-alerts
Method: POST

Body:
{
  "topic": "system-alerts",
  "title": "🔍 Alerta Prowlarr",
  "message": "{{Message}}",
  "priority": 4,
  "tags": ["prowlarr", "health"]
}

Triggers:
✅ On Health Issue
✅ On Application Update
```

### JELLYSEERR - Notificaciones de Solicitudes
**Settings → Notifications → Webhook**

```
Webhook URL: https://tankenotify.duckdns.org/media-requests
JSON Payload:
{
  "topic": "media-requests",
  "title": "📋 Nueva Solicitud",
  "message": "{{user_displayname}} solicitó {{media_type}}: {{subject}}",
  "priority": 3,
  "tags": ["request", "jellyseerr"],
  "click": "https://tankejellyseerr.duckdns.org"
}

Enable for:
✅ Media Requested
✅ Media Approved
✅ Media Available
```

## 🔒 Configuración de Seguridad

### Crear usuario en ntfy:
```bash
# Dentro del contenedor ntfy
ntfy user add --role=admin tu_usuario
ntfy user add --role=user notificaciones_user
ntfy access tu_usuario '*' rw
```

### Configurar autenticación:
1. Acceder a https://tankenotify.duckdns.org
2. Crear cuenta de administrador
3. Configurar tópicos con permisos específicos
4. Generar tokens para aplicaciones

## 📊 Tópicos y Prioridades

### Prioridades:
- **1 (Min)**: Logs de debug
- **2 (Low)**: Información general
- **3 (Default)**: Descargas completadas
- **4 (High)**: Alertas importantes
- **5 (Max)**: Errores críticos

### Tópicos organizados:
```
media-downloads     # Sonarr, Radarr downloads
media-requests      # Jellyseerr requests
system-alerts       # Health issues, errors
torrent-status      # qBittorrent notifications
app-updates         # Application updates
```

## 🚀 Comandos de Prueba

### Enviar notificación de prueba:
```bash
curl -X POST https://tankenotify.duckdns.org/media-downloads \
  -H "Content-Type: application/json" \
  -d '{
    "title": "🧪 Prueba del Sistema",
    "message": "Sistema de notificaciones funcionando correctamente",
    "priority": 3,
    "tags": ["test"]
  }'
```

### Verificar estado:
```bash
curl https://tankenotify.duckdns.org/v1/health
```
