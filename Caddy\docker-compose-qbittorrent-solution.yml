# Solución definitiva: qBittorrent en Docker para mejor compatibilidad
# Esta configuración resuelve todos los problemas de conectividad

version: '3.8'

networks:
  media-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

services:
  # qBittorrent en Docker (solución definitiva)
  qbittorrent:
    image: lscr.io/linuxserver/qbittorrent:latest
    container_name: qbittorrent-docker
    restart: unless-stopped
    
    ports:
      - "8099:8080"  # Web UI
      - "6881:6881"  # BitTorrent TCP
      - "6881:6881/udp"  # BitTorrent UDP
    
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - WEBUI_PORT=8080
    
    volumes:
      # Configuración
      - ./qbittorrent-docker/config:/config
      # Downloads (usar la misma carpeta que tu qBittorrent actual)
      - C:/Downloads:/downloads
      # Opcional: carpeta de películas
      - C:/Movies:/movies
    
    networks:
      - media-network
    
    # Configuración de salud
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Radarr conectado a la misma red
  radarr:
    image: lscr.io/linuxserver/radarr:latest
    container_name: radarr-fixed
    restart: unless-stopped
    
    ports:
      - "7878:7878"
    
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - UMASK=002
    
    volumes:
      # Configuración (copia desde tu Radarr actual)
      - ./radarr/config:/config
      # Downloads (misma carpeta que qBittorrent)
      - C:/Downloads:/downloads
      # Películas
      - C:/Movies:/movies
    
    networks:
      - media-network
    
    depends_on:
      - qbittorrent

# Con esta configuración:
# - Radarr puede acceder a qBittorrent usando: http://qbittorrent:8080
# - Ambos están en la misma red Docker
# - No hay problemas de conectividad Docker→Host
# - qBittorrent Web UI accesible en localhost:8099
